import logging
import requests
import time # 新增
from flask import Flask, request, jsonify, render_template_string, send_from_directory
import pymysql.cursors
import datetime
import json
import os
from dotenv import load_dotenv
from functools import wraps  # 用于装饰器
import os.path
import re  # 用于解析 Referer 获取 appid

# 开发环境下运行的命令
# D:\code\python\miniapps\venv\Scripts\python.exe main.py

# 导入JWT认证工具模块
from security.jwt_auth import generate_token, token_required

# 导入连接池模块的函数
from config.connection_pools import get_mailbox_db_connection, close_pools

# --- 新增：导入订阅消息发送函数 ---
from utils.mailbox_subscriptions import send_subscribe_message, send_like_notification

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    # level=logging.INFO,  # 生产环境使用 INFO 级别
    level=logging.DEBUG,  # 生产环境使用 INFO 级别 | 修改为 DEBUG 以查看详细日志
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mailbox.log', encoding='utf-8'),  # 写入文件
        logging.StreamHandler()  # 同时输出到控制台
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 配置静态文件目录
# Flask 默认会从名为 'static' 的文件夹提供服务，通常无需额外配置。
# 但如果你的静态文件在别处，或需要更精细控制，可以像下面这样设置：
# 确定当前文件的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 确定static文件夹的位置（在当前目录的上一级目录下）
static_folder = os.path.join(os.path.dirname(current_dir), 'static')
# 如果static目录不存在，创建它
if not os.path.exists(static_folder):
    try:
        os.makedirs(static_folder)
        logger.info(f"已创建static目录: {static_folder}")
    except Exception as e:
        logger.error(f"创建static目录失败: {str(e)}")

# 确保头像目录存在
avatars_folder = os.path.join(static_folder, 'avatars')
if not os.path.exists(avatars_folder):
    try:
        os.makedirs(avatars_folder)
        logger.info(f"已创建avatars目录: {avatars_folder}")
    except Exception as e:
        logger.error(f"创建avatars目录失败: {str(e)}")

# 设置Flask的static_folder
app.static_folder = static_folder
app.static_url_path = '/static'
logger.info(f"Flask静态文件目录配置为: {static_folder}, URL路径: {app.static_url_path}")

# 数据库配置
from config.database import MAILBOX_DB_CONFIG

# === 新增：辅助函数，减少代码重复 ===
# 注意：get_db_connection函数已经移除，直接使用从connection_pools导入的函数

def format_datetime(dt):
    """格式化日期时间对象为字符串"""
    if isinstance(dt, datetime.datetime):
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    return dt

def handle_db_error(e, operation_name):
    """统一处理数据库错误"""
    logger.error(f"{operation_name}失败: {str(e)}")
    return jsonify({'error': '数据库操作失败，请稍后重试'}), 500
# === 辅助函数结束 ===

# QQ小程序配置
QQ_APPID = os.getenv('MAILBOX_QQ_APPID')
QQ_SECRET = os.getenv('MAILBOX_QQ_SECRET')

# 微信小程序配置（支持多套，向下兼容）
# 主配置（保持旧变量名，兼容旧流程）
WECHAT_APPID = os.getenv('MAILBOX_WECHAT_APPID')
WECHAT_SECRET = os.getenv('MAILBOX_WECHAT_SECRET')
MAILBOX_WECHAT_LIKE_TEMPLATE_ID = os.getenv('MAILBOX_WECHAT_LIKE_TEMPLATE_ID')

# 第二套（可选）
WECHAT_APPID_2 = os.getenv('MAILBOX_WECHAT_APPID_2')
WECHAT_SECRET_2 = os.getenv('MAILBOX_WECHAT_SECRET_2')
MAILBOX_WECHAT_LIKE_TEMPLATE_ID_2 = os.getenv('MAILBOX_WECHAT_LIKE_TEMPLATE_ID_2')

# 构建统一配置表，键为 appid
WECHAT_APP_CONFIG = {}
if WECHAT_APPID:
    WECHAT_APP_CONFIG[WECHAT_APPID] = {
        'secret': WECHAT_SECRET,
        'like_template_id': MAILBOX_WECHAT_LIKE_TEMPLATE_ID
    }
if WECHAT_APPID_2:
    WECHAT_APP_CONFIG[WECHAT_APPID_2] = {
        'secret': WECHAT_SECRET_2,
        'like_template_id': MAILBOX_WECHAT_LIKE_TEMPLATE_ID_2
    }

# 解析 Referer 中的 appid（形如 https://servicewechat.com/<appid>/xxx）
_APPID_RE = re.compile(r'/([\w]{18})/')  # wx + 16位字母数字 或类似前缀

def detect_wechat_appid():
    """从请求头 Referer 中提取微信小程序的 appid，如未解析到则返回 None"""
    referer = request.headers.get('Referer', '')
    m = _APPID_RE.search(referer)
    if m:
        return m.group(1)
    return None

# 动态管理 ContentSecurityWX 实例，按 appid 缓存
content_security_wx_pool = {}

def get_content_security_wx_dynamic(appid):
    """按 appid 获取或创建 ContentSecurityWX 实例"""
    if not appid:
        # 回退到全局默认实例
        return content_security_wx

    if appid not in content_security_wx_pool:
        cfg = WECHAT_APP_CONFIG.get(appid)
        secret_val = cfg['secret'] if cfg else WECHAT_SECRET
        try:
            content_security_wx_pool[appid] = ContentSecurityWX(appid, secret_val)
        except Exception as e:
            logger.error(f"创建 ContentSecurityWX 实例失败 (appid={appid}): {e}")
            return content_security_wx  # 回退
    return content_security_wx_pool[appid]

from security.content_security import ContentSecurity
from security.content_security_wx import ContentSecurityWX

# 初始化两个平台的内容安全检查实例
content_security = ContentSecurity(QQ_APPID, QQ_SECRET)
content_security_wx = ContentSecurityWX(WECHAT_APPID, WECHAT_SECRET)

# HTML 模板
dashboard_html = """
<!-- 这里是您的仪表盘HTML模板 -->
mailbox仪表盘
"""

# 定义公告内容
CURRENT_NOTICE = {
    "show": False,  # 控制是否启用公告
    "id": "notice_welcome_v1", # 公告唯一ID，用于前端记录是否已读，暂已移除已读功能
    "title": "维护通知",
    "body": "小程序将于2025年4月10日10:00-12:00进行系统维护，届时将无法访问。\n维护期间给您带来的不便，敬请谅解！",
    # 注意：路径相对于 Flask 应用的根目录下的 static 文件夹
    "image_path": "/static/share-cover.png", # 公告图片路径
    "platforms": ["WX"]  # 公告适用的平台列表，可以是 ["WX"]、["QQ"] 或 ["WX", "QQ"]
}

# 定义用户通知内容和触发条件
USER_NOTICE_CONFIG = {
    "trigger_remark": "需要通知",  # 触发通知的remark值（管理员可以设置用户的remark为这个值）
    "notice": {
        "show": True,  # 控制是否启用该通知
        "id": "user_notice_v1",  # 通知唯一ID
        "title": "个人通知",
        "body": "您有一条个人通知，请注意查看。如有疑问，请联系管理员。",
        "image_path": "/static/share/index1.png"  # 可选，通知图片路径
    }
}

# API路由
@app.route('/mailbox/')
def home():
    """渲染主页"""
    # logger.info("访问主页")
    return render_template_string(dashboard_html)

@app.route('/mailbox/notice', methods=['GET'])
def get_notice():
    """获取当前公告信息"""
    try:
        # 获取请求平台信息（从请求参数中）
        platform = request.args.get('platform')
        
        # 如果平台参数未提供，尝试从请求头中获取 (兼容旧版本客户端)
        if not platform:
            user_agent = request.headers.get('User-Agent', '').lower()
            if 'miniprogram' in user_agent and 'qq' in user_agent:
                platform = 'QQ'
            elif 'miniprogram' in user_agent and 'micromessenger' in user_agent:
                platform = 'WX'
        
        logger.info(f"请求公告接口，平台: {platform}")
        
        if CURRENT_NOTICE.get("show", False):
            # 检查该平台是否应该显示公告
            platforms = CURRENT_NOTICE.get("platforms", ["WX", "QQ"])  # 默认两个平台都显示
            
            # 如果指定了平台并且该平台不在公告的目标平台列表中，则不显示公告
            if platform and platform not in platforms:
                logger.info(f"公告不适用于平台 {platform}，不返回公告内容")
                return jsonify({"show": False})
            
            # 如果公告启用且适用于当前平台，返回公告内容
            logger.info(f"返回公告内容给平台: {platform}")
            return jsonify(CURRENT_NOTICE)
        else:
            # 如果公告禁用，返回不显示的信息
            logger.info("请求公告接口，当前无公告")
            return jsonify({"show": False})
    except Exception as e:
        logger.error(f"获取公告信息失败：{str(e)}")
        # 即使出错，也返回不显示，避免前端出错
        return jsonify({"show": False, "error": "获取公告失败"}), 500


@app.route('/mailbox/config', methods=['GET'])
def get_app_config():
    """返回应用所需的前端配置"""
    platform = request.args.get('platform', 'WX')
    config_data = {}

    if platform == 'WX':
        # 动态选择 like_template_id，根据请求中的 appid
        req_appid = detect_wechat_appid() or WECHAT_APPID
        like_tpl = WECHAT_APP_CONFIG.get(req_appid, {}).get('like_template_id', MAILBOX_WECHAT_LIKE_TEMPLATE_ID)
        config_data['like_template_id'] = like_tpl

    logger.info(f"为平台 {platform} (appid={detect_wechat_appid()}) 提供配置: {config_data}")
    return jsonify({'success': True, 'data': config_data})


# === 新增（恢复） API 端点：获取最新留言（无需 token 认证，兼容旧版） ===
# @app.route('/mailbox/recent-messages', methods=['GET'])
# def get_recent_messages_v1_compat(): 
    """
    获取最新的5条留言 - 无需认证 (兼容旧版 App)
    
    此接口专为旧版首页快速展示设计，严格控制返回数据量，无需 token 认证
    """
    try:
        # 使用 V2 的数据库连接方式
        connection = get_mailbox_db_connection() 
        try:
            with connection.cursor() as cursor:
                # 固定查询最新的5条留言
                sql = """
                SELECT DISTINCT
                    am.*,
                    u.nickname,
                    u.avatar_url,
                    u.platform
                FROM anonymous_messages am
                JOIN users u ON am.user_id = u.id
                ORDER BY am.created_at DESC
                LIMIT 5
                """
                cursor.execute(sql)
                result = cursor.fetchall()
                
                # 使用 V2 的日期格式化辅助函数
                for message in result:
                    message['created_at'] = format_datetime(message.get('created_at')) 
                
                logger.info(f"兼容接口 /mailbox/recent-messages: 获取最新5条留言成功，数量: {len(result)}")
                # 返回列表，与 V1 行为一致
                return jsonify(result) 
        except pymysql.Error as db_error:
             # 使用 V2 的数据库错误处理辅助函数
            return handle_db_error(db_error, "获取最新留言 (兼容接口)")
        finally:
            if connection:
                connection.close()
    except Exception as e:
        # 处理获取连接等其他可能的错误
        logger.error(f"处理兼容接口 /mailbox/recent-messages 请求时发生未知错误：{str(e)}")
        return jsonify({'error': '获取最新留言失败，请稍后重试'}), 500
# === 端点恢复结束 ===



def get_platform_openid(code, platform):
    """统一获取不同平台的openid和unionid"""
    try:
        if platform == 'QQ':
            url = 'https://api.q.qq.com/sns/jscode2session'
            params = {
                'appid': QQ_APPID,
                'secret': QQ_SECRET,
                'js_code': code,
                'grant_type': 'authorization_code'
            }
        elif platform == 'WX':
            # 动态识别请求来源的 appid
            req_appid = detect_wechat_appid() or WECHAT_APPID
            # 根据 appid 取对应 secret
            secret_val = WECHAT_APP_CONFIG.get(req_appid, {}).get('secret', WECHAT_SECRET)

            url = 'https://api.weixin.qq.com/sns/jscode2session'
            params = {
                'appid': req_appid,
                'secret': secret_val,
                'js_code': code,
                'grant_type': 'authorization_code'
            }
        else:
            raise ValueError(f"不支持的平台类型: {platform}")

        response = requests.get(url, params=params)
        data = response.json()
        logger.info(f"{platform}登录请求结果：{data}")

        if 'openid' in data:
            # 同时尝试获取 unionid (QQ平台)
            unionid = data.get('unionid') if platform == 'QQ' else None
            return {'openid': data['openid'], 'unionid': unionid}
        elif 'errcode' in data:
            error_msg = data.get('errmsg', '未知错误')
            raise Exception(f"获取{platform} openid失败：{error_msg}")
        else:
            raise Exception(f"获取{platform} openid失败：未知错误")
    except requests.RequestException as e:
        logger.error(f"{platform}接口请求失败：{str(e)}")
        raise Exception(f"{platform}服务器连接失败")
    except Exception as e:
        logger.error(f"{platform}登录失败：{str(e)}")
        raise

@app.route('/mailbox/get_openid', methods=['POST'])
def get_mailbox_openid():
    """统一的openid获取接口，登录成功后返回 JWT Token"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400

        code = data.get('code')
        platform = data.get('platform')

        if not code or not platform:
            return jsonify({'error': '缺少必要参数：code或platform'}), 400

        openid_info = None
        try:
            # 使用统一的获取openid函数
            openid_info = get_platform_openid(code, platform)
        except Exception as e:
             # 捕获 get_platform_openid 抛出的异常
             logger.error(f"获取 openid 失败 ({platform}): {str(e)}")
             # 将底层错误更友好地返回给前端
             error_message = str(e)
             # 针对性处理常见错误信息
             if "获取QQ openid失败" in error_message or "获取微信openid失败" in error_message:
                 return jsonify({'error': error_message}), 401 
             else:
                 return jsonify({'error': '登录凭证校验失败，请稍后重试'}), 500

        openid = openid_info.get('openid')
        unionid = openid_info.get('unionid') # 获取 unionid

        # --- 使用JWT模块生成Token ---
        try:
            # === 在生成Token前，确保用户记录存在 ===
            connection = get_mailbox_db_connection()
            is_new_user = False # 初始化标志
            user_id_for_token = None # 用于记录用户ID，下面会用到
            try:
                with connection.cursor() as cursor:
                    # 检查用户是否存在，并获取其ID和qq_unionid
                    cursor.execute(
                        "SELECT id, qq_unionid FROM users WHERE openid = %s AND platform = %s",
                        (openid, platform)
                    )
                    user = cursor.fetchone()

                    if not user:
                        # 用户不存在，创建新用户记录
                        is_new_user = True # 设置标志为 True
                        default_nickname = "没改名的微信用户" if platform == 'WX' else "没改名的QQ用户"
                        default_avatar_url = "" # 或 '默认头像URL'
                        # 在插入语句中添加 qq_unionid
                        cursor.execute(
                            "INSERT INTO users (openid, nickname, avatar_url, platform, is_banned, remark, qq_unionid) VALUES (%s, %s, %s, %s, %s, %s, %s)",
                            (openid, default_nickname, default_avatar_url, platform, 0, None, unionid if platform == 'QQ' else None)
                        )
                        connection.commit()
                        user_id_for_token = cursor.lastrowid # 获取新插入用户的ID
                        logger.info(f"新用户首次登录，已创建记录 - UserID: {user_id_for_token}, Platform: {platform}, QQ UnionID: {'已获取' if unionid else '未获取'}")
                    else:
                        user_id_for_token = user['id'] # 获取已有用户的ID
                        logger.info(f"老用户登录 - UserID: {user_id_for_token}, Platform: {platform}")
                        # 如果是QQ平台，并且获取到了unionid，并且数据库中没有unionid或与获取到的不同，则更新
                        if platform == 'QQ' and unionid and (user.get('qq_unionid') != unionid):
                            try:
                                cursor.execute(
                                    "UPDATE users SET qq_unionid = %s WHERE id = %s",
                                    (unionid, user_id_for_token)
                                )
                                connection.commit()
                                logger.info(f"更新用户 {user_id_for_token} 的 QQ UnionID 成功")
                            except pymysql.Error as update_err:
                                connection.rollback()
                                logger.error(f"更新用户 {user_id_for_token} 的 QQ UnionID 失败: {update_err}")
            except pymysql.Error as db_error:
                connection.rollback() # 如果操作失败则回滚
                return handle_db_error(db_error, "处理用户记录")
            finally:
                if connection:
                    connection.close()
            # === 用户记录处理结束 ===

            # 准备Token载荷 - 注意：现在我们已经有了 user_id，但JWT标准做法通常不直接放ID，保持原有方式
            payload = {
                'openid': openid,
                'platform': platform
            }
            # 生成Token
            token = generate_token(payload)
            logger.info(f"用户登录成功并生成Token - UserID: {user_id_for_token}, Platform: {platform}")
            # 将 token 直接返回给前端，前端需要保存它
            return jsonify({'token': token, 'is_new_user': is_new_user})
        except Exception as e:
             logger.error(f"生成 Token 失败: {str(e)}")
             return jsonify({'error': '生成认证信息失败'}), 500
        # --- JWT Token 生成结束 ---

    except Exception as e:
        # 这个捕获是为了处理 get_json 或其他未预料的错误
        logger.error(f"处理登录请求时发生未知错误：{str(e)}")
        return jsonify({'error': '登录请求处理失败'}), 500


@app.route('/mailbox/profile', methods=['GET', 'POST'])
@token_required
def handle_user_profile():
    """处理用户个人信息的获取和更新"""
    if request.method == 'GET':
        return get_user_profile()
    else:  # POST 请求 - 更新用户信息
        return update_user_profile()

def get_user_profile():
    """获取当前登录用户的个人信息"""
    try:
        current_user = request.current_user
        # 直接从 current_user 获取用户 ID 和其他信息
        user_id = current_user['id']
        platform = current_user['platform'] # 保留platform用于日志记录
        
        connection = get_mailbox_db_connection()
        try:
            with connection.cursor() as cursor:
                # 1. 获取用户信息 (使用 user_id 查询)
                cursor.execute(
                    "SELECT nickname, avatar_url, is_banned, remark, last_login_time, updated_at FROM users WHERE id = %s",
                    (user_id,)
                )
                user_profile = cursor.fetchone()
                
                if user_profile:
                    logger.info(f"成功获取用户信息 - UserID: {user_id}, Platform: {platform}")
                    
                    # 2. 在获取成功后，更新用户的 last_login_time
                    try:
                        cursor.execute(
                            "UPDATE users SET last_login_time = NOW() WHERE id = %s",
                            (user_id,)
                        )
                        connection.commit()
                        logger.info(f"已更新用户登录时间 - UserID: {user_id}")
                    except Exception as update_err:
                        connection.rollback() # 如果更新失败则回滚
                        logger.error(f"在 get_user_profile 中更新登录时间失败: {str(update_err)}")
                        # 这里不中断流程，获取用户信息仍然可以继续
                    
                    # 返回用户信息，使用辅助函数格式化日期
                    return jsonify({
                        'nickname': user_profile['nickname'],
                        'avatarUrl': user_profile['avatar_url'], # 字段名转换
                        'is_banned': user_profile['is_banned'],
                        'remark': user_profile['remark'],
                        'lastLoginTime': format_datetime(user_profile['last_login_time']),
                        'updatedAt': format_datetime(user_profile['updated_at'])
                    })
                else:
                    # 理论上用户应该存在，因为Token验证时已经查过
                    logger.error(f"获取用户信息失败：数据库中未找到用户(Token验证后) - UserID: {user_id}, Platform: {platform}")
                    return jsonify({'error': '无法获取用户信息'}), 404
        except pymysql.Error as db_error:
            return handle_db_error(db_error, "查询用户信息")
        finally:
            if connection:
                connection.close()

    except KeyError: # 捕获 current_user['id'] 不存在的情况
        logger.error("Token 验证通过，但未能获取用户 ID (可能是 jwt_auth 模块数据库错误)")
        return jsonify({'error': '用户认证信息不完整，请稍后重试'}), 500
    except Exception as e:
        logger.error(f"处理获取用户信息请求时发生未知错误: {str(e)}")
        return jsonify({'error': '获取用户信息请求处理失败'}), 500

def update_user_profile():
    """更新用户个人信息 - 处理昵称更新和头像上传"""
    try:
        current_user = request.current_user
        # 直接从 current_user 获取用户 ID 和其他信息
        user_id = current_user['id']
        platform = current_user['platform'] # 保留用于日志
        
        # 初始化要更新的数据
        update_data = {}
        avatar_url = None
        
        # 检查是否为文件上传请求
        if 'avatarFile' in request.files:
            try:
                avatar_file = request.files['avatarFile']
                
                # 记录上传文件信息
                logger.info(f"收到头像上传请求 - UserID:: {user_id}, 文件名: {avatar_file.filename}, 类型: {avatar_file.content_type if hasattr(avatar_file, 'content_type') else '未知'}")
                
                if avatar_file and avatar_file.filename:
                    # 检查文件内容是否为空
                    file_content = avatar_file.read()
                    if not file_content or len(file_content) < 100:  # 简单检查文件内容是否太小
                        logger.error(f"头像文件内容为空或过小 - UserID:: {user_id}, 大小: {len(file_content) if file_content else 0} 字节")
                        return jsonify({'error': '上传的头像文件无效或为空'}), 400
                    
                    # 重置文件指针位置，以便后续保存
                    avatar_file.seek(0)
                    
                    # 生成唯一文件名
                    timestamp = int(datetime.datetime.now().timestamp())
                    filename = f"user_{user_id}_{timestamp}.jpg"
                    filepath = os.path.join(avatars_folder, filename)
                    
                    # 保存文件
                    try:
                        avatar_file.save(filepath)
                        file_exists = os.path.exists(filepath)
                        file_size = os.path.getsize(filepath) if file_exists else 0
                        
                        logger.info(f"头像文件保存结果 - 路径: {filepath}, 存在: {file_exists}, 大小: {file_size} 字节")
                        
                        if not file_exists or file_size < 100:
                            logger.error(f"头像文件保存失败或文件过小 - UserID: {user_id}, 路径: {filepath}")
                            return jsonify({'error': '头像保存失败，请重试'}), 500
                    except Exception as save_err:
                        logger.error(f"保存头像文件时出错: {str(save_err)}")
                        return jsonify({'error': '头像保存失败，请重试'}), 500
                    
                    # 生成访问URL
                    avatar_url = f"/static/avatars/{filename}"
                    update_data['avatar_url'] = avatar_url
                    logger.info(f"头像文件上传成功 - UserID: {user_id}, 最终URL: {avatar_url}")
                else:
                    logger.warning(f"头像文件无效 - UserID: {user_id}, 文件名为空或文件对象为None")
                    return jsonify({'error': '请选择有效的头像文件'}), 400
            except Exception as e:
                logger.error(f"处理头像文件上传失败: {str(e)}")
                return jsonify({'error': '头像上传失败，请稍后重试'}), 500
        else:
            logger.info(f"请求中没有头像文件 - UserID: {user_id}, Content-Type: {request.content_type}, 是否有form数据: {bool(request.form)}")
        
        # 检查是否更新昵称
        nickname = None
        if request.form and 'nickname' in request.form:
             nickname = request.form['nickname']
             logger.info(f"从表单获取到昵称 - UserID: {user_id}, 昵称: {nickname}")
        elif request.is_json and request.json and 'nickname' in request.json:
             nickname = request.json['nickname']
             logger.info(f"从JSON获取到昵称 - UserID: {user_id}, 昵称: {nickname}")
        
        if nickname:
             update_data['nickname'] = nickname
             
        # 检查是否有QQ头像URL更新 (已修改为下载后上传，此逻辑保留但应不再使用)
        if request.is_json and request.json and 'avatarUrl' in request.json:
            avatar_url = request.json['avatarUrl']
            if avatar_url:
                logger.warning(f"检测到通过URL更新头像(不推荐) - UserID: {user_id}, URL: {avatar_url}")
                update_data['avatar_url'] = avatar_url
        
        # 如果没有任何有效更新数据，返回成功但提示无变化
        if not update_data:
            logger.warning(f"没有可更新的数据 - UserID: {user_id}")
            return jsonify({'message': '没有发现需要更新的信息', 'nickname': None, 'avatarUrl': None, 'updatedAt': None}), 200
        
        # 更新数据库
        connection = get_mailbox_db_connection() # 获取新的连接
        try:
            with connection.cursor() as cursor:
                # 构建SQL更新语句
                sql_parts = []
                params = []
                
                for key, value in update_data.items():
                    sql_parts.append(f"{key} = %s")
                    params.append(value)
                
                # 显式添加 updated_at 更新
                sql_parts.append("updated_at = NOW()")
                    
                params.append(user_id)  # 用于WHERE条件
                
                sql = f"UPDATE users SET {', '.join(sql_parts)} WHERE id = %s"
                logger.info(f"执行SQL更新 - UserID: {user_id}, SQL: {sql}, 参数(不含ID): {params[:-1]}")
                
                rows_affected = cursor.execute(sql, params)
                logger.info(f"更新操作影响的行数: {rows_affected}")
                
                if rows_affected > 0:
                    connection.commit()
                    logger.info(f"数据库提交成功 - UserID: {user_id}")
                else:
                    # 虽然理论上应该至少更新 updated_at，但也可能因为 where id 不匹配等原因失败
                    logger.warning(f"更新操作未影响任何行 - UserID: {user_id}，可能用户不存在或无需更新")
                    connection.rollback() # 回滚以防万一
                    # 即使没更新成功，也尝试查询一下当前用户信息返回

                # 查询更新后的用户信息，包括更新时间
                cursor.execute(
                    "SELECT nickname, avatar_url, updated_at FROM users WHERE id = %s",
                    (user_id,)
                )
                updated_user = cursor.fetchone()
                
                if updated_user:
                    # 使用辅助函数格式化时间
                    updated_at = format_datetime(updated_user.get('updated_at'))
                    
                    logger.info(f"用户信息更新成功(或查询成功) - UserID: {user_id}, 昵称: {updated_user['nickname']}, 头像URL: {updated_user['avatar_url']}, 更新时间: {updated_at}")
                    return jsonify({
                        'message': '更新成功' if rows_affected > 0 else '用户信息已是最新',
                        'nickname': updated_user['nickname'],
                        'avatarUrl': updated_user['avatar_url'],
                        'updatedAt': updated_at
                    })
                else:
                    # 如果连查询都失败了，说明用户可能真的不见了？
                    logger.error(f"用户信息更新后查询失败 - UserID: {user_id}")
                    return jsonify({'error': '更新或查询用户信息时出错'}), 500
                
        except pymysql.Error as db_error:
            connection.rollback()
            return handle_db_error(db_error, "更新用户信息")
        finally:
            if connection:
                connection.close()
                
    except KeyError: # 捕获 current_user['id'] 不存在的情况
        logger.error("Token 验证通过，但未能获取用户 ID (可能是 jwt_auth 模块数据库错误)")
        return jsonify({'error': '用户认证信息不完整，请稍后重试'}), 500
    except Exception as e:
        # 这个捕获是为了处理 request.current_user 或 get_connection 的错误
        logger.error(f"处理更新用户信息请求时发生顶层错误: {str(e)}")
        return jsonify({'error': '更新用户信息请求处理失败'}), 500


@app.route('/mailbox/stats', methods=['GET'])
@token_required
def get_user_stats():
    """获取当前登录用户的统计信息（已发送数量和获得点赞数）"""
    try:
        current_user = request.current_user
        # 直接从 current_user 获取用户 ID
        user_id = current_user['id']
        platform = current_user['platform'] # 保留用于日志

        connection = get_mailbox_db_connection()
        try:
            with connection.cursor() as cursor:
                # 统计已发送留言数量
                cursor.execute(
                    "SELECT COUNT(*) as sent_count FROM anonymous_messages WHERE user_id = %s",
                    (user_id,)
                )
                stats_result = cursor.fetchone()
                sent_count = stats_result['sent_count'] if stats_result else 0

                # 统计获得的总点赞数
                cursor.execute("""
                    SELECT COUNT(*) as total_likes
                    FROM message_likes ml
                    JOIN anonymous_messages am ON ml.message_id = am.id
                    WHERE am.user_id = %s
                """, (user_id,))
                likes_result = cursor.fetchone()
                total_likes = likes_result['total_likes'] if likes_result else 0

                logger.info(f"成功获取用户统计信息 - UserID: {user_id}, Platform: {platform}, 已发送数量: {sent_count}, 获得点赞: {total_likes}")
                return jsonify({
                    'sentCount': sent_count,
                    'totalLikes': total_likes
                })

        except pymysql.Error as db_error:
            return handle_db_error(db_error, "查询用户统计")
        finally:
            if connection:
                connection.close()

    except KeyError: # 捕获 current_user['id'] 不存在的情况
        logger.error("Token 验证通过，但未能获取用户 ID (可能是 jwt_auth 模块数据库错误)")
        return jsonify({'error': '用户认证信息不完整，请稍后重试'}), 500
    except Exception as e:
        logger.error(f"处理获取用户统计请求时发生未知错误: {str(e)}")
        return jsonify({'error': '获取用户统计请求处理失败'}), 500


@app.route('/mailbox/my-messages', methods=['GET'])
@token_required
def get_my_messages():
    """获取当前登录用户发送的消息列表"""
    try:
        current_user = request.current_user
        # 直接从 current_user 获取用户 ID
        user_id = current_user['id']
        platform = current_user['platform'] # 保留用于日志
        
        # 获取查询参数
        limit = request.args.get('limit', type=int) # 可选的 limit 参数
        
        connection = get_mailbox_db_connection()
        try:
            with connection.cursor() as cursor:
                # 查询该用户发送的消息，包含点赞数
                base_sql = """
                    SELECT id, name, content, created_at, like_count
                    FROM anonymous_messages
                    WHERE user_id = %s
                    ORDER BY created_at DESC
                """
                params = (user_id,)
                
                # 如果提供了 limit 参数，则应用 LIMIT
                if limit is not None and limit > 0:
                    sql = base_sql + " LIMIT %s"
                    params += (limit,)
                else: # 获取全部
                    sql = base_sql

                cursor.execute(sql, params)
                messages = cursor.fetchall()

                # 格式化日期时间
                for message in messages:
                    message['created_at'] = format_datetime(message.get('created_at'))
                
                logger.info(f"成功获取用户 {user_id} 的 {len(messages)} 条消息 (Platform: {platform}, 限制: {limit if limit else '无'})")
                return jsonify(messages)

        except pymysql.Error as db_error:
            return handle_db_error(db_error, "查询我的消息")
        finally:
            if connection:
                connection.close()

    except KeyError: # 捕获 current_user['id'] 不存在的情况
        logger.error("Token 验证通过，但未能获取用户 ID (可能是 jwt_auth 模块数据库错误)")
        return jsonify({'error': '用户认证信息不完整，请稍后重试'}), 500
    except Exception as e:
        logger.error(f"处理获取我的消息请求时发生未知错误: {str(e)}")
        return jsonify({'error': '获取消息请求处理失败'}), 500


# 删除handle_mailbox_messages函数
@app.route('/mailbox/random-messages', methods=['GET'])
@token_required
def get_random_messages():
    """获取随机留言列表"""
    try:
        current_user = request.current_user
        user_id = current_user.get('id')
        platform = current_user.get('platform')
        
        # 获取查询参数
        count = min(int(request.args.get('count', 5)), 20)  # 限制最多20条
        first_char = request.args.get('first_char', '')  # 新增：获取首字参数，用于同姓氏搜索
        
        connection = get_mailbox_db_connection()
        try:
            with connection.cursor() as cursor:
                # 判断是否进行同姓氏搜索
                if first_char and len(first_char) > 0:
                    # 获取同姓氏的留言
                    sql = """
                    SELECT 
                        am.id, 
                        am.name, 
                        am.content, 
                        am.created_at,
                        am.like_count,
                        am.show_author,
                        u.nickname,
                        u.avatar_url,
                        u.platform
                    FROM anonymous_messages am
                    JOIN users u ON am.user_id = u.id
                    WHERE am.name LIKE %s
                    ORDER BY RAND()
                    LIMIT %s
                    """
                    cursor.execute(sql, (f"{first_char}%", count))
                    logger.info(f"用户 {user_id} (Platform: {platform}) 获取了首字为 '{first_char}' 的留言")
                else:
                    # 无首字参数时，获取随机留言
                    sql = """
                    SELECT 
                        am.id, 
                        am.name, 
                        am.content, 
                        am.created_at,
                        am.like_count,
                        am.show_author,
                        u.nickname,
                        u.avatar_url,
                        u.platform
                    FROM anonymous_messages am
                    JOIN users u ON am.user_id = u.id
                    ORDER BY RAND()
                    LIMIT %s
                    """
                    cursor.execute(sql, (count,))
                    logger.info(f"用户 {user_id} (Platform: {platform}) 获取了 {count} 条随机留言")
                
                messages = cursor.fetchall()
                
                # 格式化日期时间
                for message in messages:
                    message['created_at'] = format_datetime(message.get('created_at'))
                    
                    # 根据 show_author 字段决定是否返回作者信息
                    if message.get('show_author') == 1:
                        message['author_info'] = sanitize_author_info(
                            message.get('nickname'),
                            message.get('avatar_url')
                        )
                    
                    # 从响应中移除不需要直接暴露的字段
                    if 'show_author' in message:
                        del message['show_author']
                    if 'nickname' in message:
                        del message['nickname']
                    if 'avatar_url' in message:
                        del message['avatar_url']
                
                # 查询用户对这些消息的点赞状态
                message_ids = [message['id'] for message in messages]
                user_likes = check_user_liked_messages(cursor, user_id, message_ids)
                
                # 将点赞状态添加到消息数据中
                for message in messages:
                    message['is_liked_by_user'] = user_likes.get(message['id'], False)
                
                # 构建响应，增加首字符信息
                response_data = {
                    'success': True,
                    'data': messages
                }
                
                if first_char:
                    response_data['first_char'] = first_char
                
                return jsonify(response_data)
                
        except pymysql.Error as db_error:
            return handle_db_error(db_error, "获取留言")
        finally:
            if connection:
                connection.close()
                
    except Exception as e:
        logger.error(f"处理获取留言请求时发生未知错误: {str(e)}")
        return jsonify({'error': '获取留言失败，请稍后重试'}), 500


# 为add_mailbox_message添加路由装饰器
@app.route('/mailbox/messages', methods=['POST'])
@token_required # 应用装饰器
def add_mailbox_message():
    """添加新消息 (此函数现在受 token_required 保护)"""
    try:
        # 从请求上下文中获取用户信息 (由 token_required 装饰器添加)
        current_user = request.current_user 
        # 直接从 current_user 获取用户 ID 和其他信息
        user_id = current_user['id']
        platform = current_user['platform']

        # 添加用户禁用状态检查
        connection = get_mailbox_db_connection()
        try:
            with connection.cursor() as cursor:
                # 查询用户的禁用状态
                cursor.execute(
                    "SELECT is_banned, remark FROM users WHERE id = %s",
                    (user_id,)
                )
                user_info = cursor.fetchone()
                
                # 检查用户是否被禁用
                if user_info and user_info['is_banned']:
                    ban_reason = user_info['remark'] or '违反留言规范'
                    logger.warning(f"被禁用用户尝试提交留言 - UserID: {user_id}, Platform: {platform}, 禁用原因: {ban_reason}")
                    return jsonify({'error': f'您的账号已被禁用，无法提交留言。原因：{ban_reason}', 'code': 'user_banned'}), 403
        except pymysql.Error as db_error:
            logger.error(f"检查用户禁用状态时发生数据库错误 - UserID: {user_id}, 错误: {str(db_error)}")
            # 如果查询禁用状态失败，继续处理（宽松处理，避免因为查询错误而阻止正常用户）
        finally:
            if connection:
                connection.close()

        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400

        # 验证必要字段
        required_fields = ['name', 'content']
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            return jsonify({'error': f'缺少必要字段：{", ".join(missing_fields)}'}), 400

        # 提取数据
        name = data['name']
        content = data['content']
        show_author = data.get('show_author', False)  # 获取是否显示作者信息的参数，默认为False

        # 内容安全检查
        try:
            # 因为openid在内容安全检查API中是必需的，所以这里保留，但日志中不再记录
            openid = current_user['openid']  # 仅用于内容安全检查API
            
            if platform == 'QQ':
                # QQ 检查使用 user_id 作为 uid
                name_check = content_security.msg_sec_check(name, str(user_id)) 
                content_check = content_security.msg_sec_check(content, str(user_id))
            elif platform == 'WX':
                # 优先使用 token 中的 wx_appid，其次 Referer
                req_appid = current_user.get('wx_appid') if isinstance(current_user, dict) else None
                if not req_appid:
                    req_appid = detect_wechat_appid() or WECHAT_APPID

                dynamic_cs = get_content_security_wx_dynamic(req_appid)

                # 微信检查需要 openid 和 scene
                name_check = dynamic_cs.msg_sec_check(name, openid, 1, str(user_id))  # scene=1
                content_check = dynamic_cs.msg_sec_check(content, openid, 2, str(user_id))
            else:
                logger.warning(f"不支持的平台类型：{platform}，跳过内容检查")
                name_check = True
                content_check = True

            if not name_check:
                logger.warning(f"用户 {user_id} (Platform: {platform}) 提交的名称 '{name}' 未通过安全检查")
                return jsonify({'error': '名称中包含不适当信息，请修改后重试'}), 400
            if not content_check:
                logger.warning(f"用户 {user_id} (Platform: {platform}) 提交的内容 '{content[:50]}...' 未通过安全检查")
                return jsonify({'error': '留言内容包含不适当信息，请修改后重试'}), 400

        except Exception as e:
            # 记录详细错误日志
            logger.error(f"内容安全检查接口调用失败 - UserID: {user_id}, Platform: {platform}, 错误: {str(e)}")
            
            # 检查是否是特定的、可恢复的错误 (例如微信 session 超时)
            # 注意：这里仅针对微信平台和特定错误消息进行处理
            if platform == 'WX' and '用户访问记录超时' in str(e): 
                 logger.warning(f"微信 Session 可能超时 - UserID: {user_id}")
                 # 返回 400 Bad Request，并附带特定错误码，提示前端可能需要重新登录
                 return jsonify({'error': '微信认证信息可能已过期，请尝试重新登录后再试', 'code': 'wx_session_timeout'}), 400 

            # 对于其他所有检查异常 (网络、SSL、API内部错误等)，都改为仅记录警告并允许提交
            logger.warning(f"内容安全检查异常：{str(e)}。允许内容提交：用户ID {user_id} (Platform: {platform})，内容: '{content[:50]}...'")
            # 出错时暂时允许提交，但需要记录日志
            # 返回 503 Service Unavailable，表示后端依赖的服务（内容安全检查）暂时不可用
            # return jsonify({'error': '内容安全检查服务暂时无法访问，请稍后重试', 'code': 'content_security_unavailable'}), 503 

        # --- 如果内容安全检查通过，或者在上面返回了，则继续执行插入 ---

        # 插入新消息
        connection = get_mailbox_db_connection()
        try:
            with connection.cursor() as cursor:
                cursor.execute(
                    "INSERT INTO anonymous_messages (name, content, user_id, show_author) VALUES (%s, %s, %s, %s)",
                    (name, content, user_id, 1 if show_author else 0)
                )
                connection.commit()

                logger.info(f"消息添加成功 - UserID: {user_id}, Platform: {platform}, 名称: '{name}', 显示作者: {show_author}")
                return jsonify({'message': '留言提交成功'})

        except pymysql.Error as db_error:
            connection.rollback()
            return handle_db_error(db_error, "插入新消息")
        finally:
            if connection:
                connection.close()

    except KeyError: # 捕获 current_user['id'] 不存在的情况
        logger.error("Token 验证通过，但未能获取用户 ID (可能是 jwt_auth 模块数据库错误)")
        return jsonify({'error': '用户认证信息不完整，请稍后重试'}), 500
    except Exception as e:
        logger.error(f"添加消息失败（受保护的端点）：{str(e)}")
        return jsonify({'error': '留言提交失败，请稍后重试', 'code': 'unknown_error'}), 500


# 在已有的路由之前，添加点赞相关辅助函数
def check_user_liked_messages(cursor, user_id, message_ids):
    """
    检查用户是否对指定消息列表进行过点赞
    
    Args:
        cursor: 数据库游标
        user_id: 用户ID
        message_ids: 消息ID列表
    
    Returns:
        dict: 键为消息ID，值为布尔值表示是否点赞过
    """
    if not message_ids:
        return {}
    
    # 构建问号占位符的字符串，例如对于3个ID会构建成 "?,?,?"
    placeholders = ','.join(['%s'] * len(message_ids))
    
    # 查询用户对这些消息的点赞记录
    sql = f"""
    SELECT message_id 
    FROM message_likes 
    WHERE user_id = %s AND message_id IN ({placeholders})
    """
    
    # 将用户ID作为第一个参数，然后是所有消息ID
    params = [user_id] + message_ids
    
    cursor.execute(sql, params)
    liked_messages = cursor.fetchall()
    
    # 构建结果字典 {消息ID: 是否点赞}
    result = {message_id: False for message_id in message_ids}
    for row in liked_messages:
        result[row['message_id']] = True
    
    return result

# 在已有路由之后添加点赞API
@app.route('/mailbox/messages/<int:message_id>/toggle_like', methods=['POST'])
@token_required
def toggle_message_like(message_id):
    """
    切换消息的点赞状态（点赞/取消点赞）
    
    该操作在数据库事务中进行，确保消息点赞表和消息表中的点赞计数保持一致
    如果发生点赞操作，且被点赞者为微信用户，则尝试发送订阅消息通知
    """
    author_user_id = None # 新增：用于在函数范围内传递作者ID
    author_name = None # 新增：用于在函数范围内传递作者name
    liker_nickname = "某位朋友" # 点赞者昵称默认值
    like_id_for_notes = None # 新增：用于记录点赞记录的ID，以便更新备注

    try:
        current_user = request.current_user
        user_id = current_user['id']
        
        # --- 新增：获取点赞者昵称 ---
        conn_liker = get_mailbox_db_connection()
        try:
            with conn_liker.cursor() as cursor_liker:
                cursor_liker.execute("SELECT nickname FROM users WHERE id = %s", (user_id,))
                liker_info = cursor_liker.fetchone()
                if liker_info and liker_info['nickname']:
                    liker_nickname = liker_info['nickname']
        except Exception as e_liker:
            logger.error(f"获取点赞者昵称失败 (UserID: {user_id}): {e_liker}")
            # 获取失败不影响核心流程，使用默认昵称
        finally:
            if conn_liker:
                conn_liker.close()
        # --- 获取昵称结束 ---
        
        # 获取数据库连接
        connection = get_mailbox_db_connection()
        connection.begin()  # 开启事务
        
        action = None # 记录是点赞还是取消点赞
        message_content_snippet = "" # 留言内容摘要
        
        try:
            with connection.cursor() as cursor:
                # --- 修改：查询消息时，同时获取作者的 user_id 和 name, content ---
                cursor.execute(
                    "SELECT id, user_id, name, content, like_count FROM anonymous_messages WHERE id = %s",
                    (message_id,)
                )
                message = cursor.fetchone()
                
                if not message:
                    connection.rollback()
                    return jsonify({'error': '找不到指定的留言'}), 404
                
                # 获取消息内容摘要
                message_content_snippet = message['content'][:20] + ('...' if len(message['content']) > 20 else '') # 取前20个字符作为摘要
                
                author_user_id = message['user_id'] # 获取作者的 user_id
                author_name = message['name'] # 获取作者的名字，用于跳转页面

                # 检查用户是否已经点赞过该消息
                cursor.execute(
                    "SELECT id FROM message_likes WHERE user_id = %s AND message_id = %s",
                    (user_id, message_id)
                )
                existing_like = cursor.fetchone()
                
                if existing_like:
                    # 用户已点赞，执行取消点赞操作
                    cursor.execute(
                        "DELETE FROM message_likes WHERE id = %s",
                        (existing_like['id'],)
                    )
                    like_id_for_notes = existing_like['id'] # 取消点赞时也记录一下ID，虽然不会发送通知
                    
                    # 更新消息的点赞计数（确保不会小于0）
                    cursor.execute(
                        "UPDATE anonymous_messages SET like_count = GREATEST(like_count - 1, 0) WHERE id = %s",
                        (message_id,)
                    )
                    
                    action = 'unliked'  # 取消点赞
                    logger.info(f"用户 {user_id} ({liker_nickname}) 取消了对留言 {message_id} (作者ID: {author_user_id}) 的点赞")
                else:
                    # 用户未点赞，执行点赞操作
                    cursor.execute(
                        "INSERT INTO message_likes (user_id, message_id) VALUES (%s, %s)",
                        (user_id, message_id)
                    )
                    like_id_for_notes = cursor.lastrowid # 获取新插入点赞记录的ID
                    
                    # 更新消息的点赞计数
                    cursor.execute(
                        "UPDATE anonymous_messages SET like_count = like_count + 1 WHERE id = %s",
                        (message_id,)
                    )
                    
                    action = 'liked'  # 点赞
                    logger.info(f"用户 {user_id} ({liker_nickname}) 点赞了留言 {message_id} (作者ID: {author_user_id})")
                    
                    # --- 新增：准备发送通知所需的信息 ---
                    # 移除这里的数据库查询逻辑，因为它已经移到 send_like_notification 中
                    # --- 准备信息结束 ---
                
                # 获取更新后的点赞数
                cursor.execute(
                    "SELECT like_count FROM anonymous_messages WHERE id = %s",
                    (message_id,)
                )
                updated_count = cursor.fetchone()['like_count']
                
                # 提交事务
                connection.commit()
                
                # 构造基础响应数据
                response_data = {
                    'success': True,
                    'action': action,
                    'message_id': message_id,
                    'like_count': updated_count
                }
                
        except pymysql.Error as db_error:
            # 回滚事务
            connection.rollback()
            logger.error(f"点赞操作数据库错误: {str(db_error)}")
            return handle_db_error(db_error, "点赞操作")
        finally:
            if connection:
                connection.close()

        # --- 修改：在数据库操作成功后，调用新的通知函数 ---
        # 只有在点赞成功、且点赞者不是作者本人的情况下才触发
        if action == 'liked' and user_id != author_user_id:
            try:
                # 调用集中的通知函数，它将处理所有后续逻辑
                send_like_notification(
                    author_user_id=author_user_id,
                    author_name=author_name,
                    message_content_snippet=message_content_snippet,
                    liker_nickname=liker_nickname,
                    like_id=like_id_for_notes
                )
            except Exception as e:
                # 记录调用通知函数时发生的意外错误，但不影响主请求的响应
                logger.error(f"调用 send_like_notification 函数时发生意外错误: {e}")
        # --- 发送通知逻辑简化结束 ---

        # 返回基础响应
        return jsonify(response_data)
                
    except Exception as e:
        logger.error(f"处理点赞请求时发生未知错误: {str(e)}")
        return jsonify({'error': '处理点赞请求失败'}), 500

# 修改获取留言列表的API，添加点赞相关信息
@app.route('/mailbox/messages', methods=['GET'])
@token_required  
def get_mailbox_messages():
    """
    获取消息列表 - 通用搜索逻辑

    搜索策略：
    1. 无关键词 
       - 若是首页特定请求（page=1 且 pageSize=5），返回最新5条留言
       - 其他情况下拒绝空查询请求
    2. 有关键词 -> 按照以下顺序匹配：
       a. 完全匹配
       b. 包含完整关键词
       c. 对于2个字符以上的中文，尝试首字匹配（通过正则判断）
    """
    try:
        # 可以选择性地获取用户信息，例如用于日志或未来可能的个性化排序
        current_user = request.current_user
        user_id = current_user.get('id') # 安全获取
        platform = current_user.get('platform')
        logger.info(f"用户 {user_id} (Platform: {platform}) 搜索消息")
        
        # 添加用户禁用状态检查
        connection = get_mailbox_db_connection()
        try:
            with connection.cursor() as cursor:
                # 检查用户是否被禁用
                cursor.execute(
                    "SELECT id, is_banned, remark FROM users WHERE id = %s", 
                    (user_id,)
                )
                user_info = cursor.fetchone()
                
                if user_info and user_info['is_banned'] == 1:
                    # 用户被禁用，返回错误信息
                    logger.warning(f"被禁用的用户 {user_id} 尝试搜索留言")
                    return jsonify({
                        'error': '您的账号已被禁用，无法使用留言功能',
                        'remark': user_info.get('remark', '如有疑问，请联系管理员')
                    }), 403
                
                # 获取查询参数
                query = request.args.get('query', '').strip() # 关键词搜索
                page = int(request.args.get('page', 1)) # 页码
                page_size = int(request.args.get('pageSize', 5)) # 每页数量

                # 页码和每页数量限制
                page = max(1, page)  # 确保页码至少为1
                page_size = min(max(1, page_size), 50)  # 确保每页数量在1-50之间
                offset = (page - 1) * page_size
                
                # 安全检查：如果 query 为空，仅允许首页特定请求（page=1, pageSize=5）
                if not query:
                    # 检查是否是首页的特定请求
                    if page == 1 and page_size == 5:
                        logger.info(f"用户 {user_id} 请求首页最新5条留言")
                        # 允许继续执行，不添加任何搜索条件
                    else:
                        # 非首页特定请求的空查询，视为无效，返回错误
                        logger.warning(f"拒绝了空 query 的 API 请求 (非首页默认请求): page={page}, pageSize={page_size}, 用户ID: {user_id}")
                        return jsonify({
                            'success': False, 
                            'error': '搜索名称不能为空', 
                            'data': [],
                            'pagination': {
                                'page': 1,
                                'pageSize': 0,
                                'total': 0,
                                'totalPages': 0
                            }
                        }), 400
                
                # 新增：关键词合法性校验，仅允许中文、字母、数字，最长20字符
                if query and (len(query) > 20 or not re.match(r'^[A-Za-z0-9\u4e00-\u9fa5]+$', query)):
                    logger.warning(f"拒绝非法搜索关键词: {query}")
                    return jsonify({
                        'success': False,
                        'error': '搜索名称包含无效字符',
                        'data': [],
                        'pagination': {
                            'page': 1,
                            'pageSize': 0,
                            'total': 0,
                            'totalPages': 0
                        }
                    }), 400
                
                # 基本的SQL查询
                base_sql = """
                SELECT 
                    am.id, 
                    am.name, 
                    am.content, 
                    am.created_at,
                    am.like_count,
                    am.show_author,
                    u.nickname,
                    u.avatar_url,
                    u.platform 
                FROM anonymous_messages am
                JOIN users u ON am.user_id = u.id
                """
                
                count_sql = """
                SELECT COUNT(*) as total
                FROM anonymous_messages am
                JOIN users u ON am.user_id = u.id
                """
                
                where_clause = ""
                params = []
                
                # 如果有搜索关键词，构建搜索条件
                if query:
                    # 检查查询是否为2个字符以上的中文字符
                    is_chinese_query = all('\u4e00' <= c <= '\u9fa5' for c in query) and len(query) >= 2
                    
                    # 构建搜索条件
                    conditions = []
                    
                    # 1. 名称完全匹配
                    conditions.append("am.name = %s")
                    params.append(query)
                    
                    # 2. 名称包含关键词
                    conditions.append("am.name LIKE %s")
                    params.append(f'%{query}%')
                    
                    # 注意：首字匹配逻辑已移到另一个流程中，不再在这里直接执行
                    # 这样可以先尝试精准匹配，找不到再通过首字匹配推荐
                    
                    # 组合搜索条件
                    where_clause = "WHERE (" + " OR ".join(conditions) + ")"
                    
                # 添加排序和分页
                order_by = "ORDER BY am.created_at DESC"
                limit_clause = "LIMIT %s OFFSET %s"
                
                # 执行查询
                sql = f"{base_sql} {where_clause} {order_by} {limit_clause}"
                cursor.execute(sql, params + [page_size, offset])
                messages = cursor.fetchall()
                
                # 格式化日期时间
                for message in messages:
                    message['created_at'] = format_datetime(message.get('created_at'))
                    
                    # 根据 show_author 字段决定是否返回作者信息
                    if message.get('show_author') == 1:
                        message['author_info'] = sanitize_author_info(
                            message.get('nickname'),
                            message.get('avatar_url')
                        )
                    
                    # 从响应中移除不需要直接暴露的字段
                    if 'show_author' in message:
                        del message['show_author']
                    if 'nickname' in message:
                        del message['nickname']
                    if 'avatar_url' in message:
                        del message['avatar_url']
                
                # 查询用户对这些消息的点赞状态
                message_ids = [message['id'] for message in messages]
                user_likes = check_user_liked_messages(cursor, user_id, message_ids)
                
                # 将点赞状态添加到消息数据中
                for message in messages:
                    message['is_liked_by_user'] = user_likes.get(message['id'], False)
                
                # 查询总数
                count_sql = f"{count_sql} {where_clause}"
                cursor.execute(count_sql, params)
                total_count = cursor.fetchone()['total']
                
                # 构建响应
                response = {
                    'success': True,
                    'data': messages,
                    'pagination': {
                        'page': page,
                        'pageSize': page_size,
                        'total': total_count,
                        'totalPages': (total_count + page_size - 1) // page_size,
                        # --- 修改: 添加 hasMore 字段 --- 
                        'hasMore': (page * page_size) < total_count 
                    }
                }
                
                logger.info(f"用户 {user_id} 搜索结果: 找到 {len(messages)} 条消息，关键词: '{query}'")
                return jsonify(response)
            
        except pymysql.Error as db_error:
            return handle_db_error(db_error, "搜索消息")
        finally:
            if connection:
                connection.close()
    except Exception as e:
        logger.error(f"处理消息搜索请求时发生未知错误: {str(e)}")
        return jsonify({'error': '请求处理失败，请稍后重试'}), 500


def is_chinese_chars(text):
    """判断是否为纯中文字符"""
    return all('\u4e00' <= char <= '\u9fa5' for char in text)


@app.route('/mailbox/typing-texts', methods=['GET'])
def get_typing_texts():
    """获取打字机效果展示文字"""
    try:
        # 此接口无需认证，移除用户信息获取
        
        texts = [
            # "字句没有署名，却带着心跳...",
            "你在读谁，谁在写你？",
            "风吹哪页读哪页...",
            "字比人勇敢，敢说不敢说的话...",
            "你是谁不重要，你写下的才重要...",
            "你今天快乐了吗?..."]
        return jsonify({
            'success': True,
            'data': texts
        })
    except Exception as e:
        logger.error(f"获取打字机文字失败：{str(e)}")
        return jsonify({
            'success': False,
            'error': '获取文字失败，请稍后重试'
        }), 500


@app.route('/mailbox/share-config', methods=['GET'])
def get_share_config():
    """获取分享配置信息"""
    try:
        # 获取页面类型和其他参数
        page_key = request.args.get('pageKey', 'index')
        name = request.args.get('name', '')
        
        # 记录详细的请求信息
        logger.debug(f"接收到分享配置请求 - 页面: {page_key}, 名称: '{name}', 客户端IP: {request.remote_addr}, User-Agent: {request.headers.get('User-Agent', '未知')}")
        
        # 检查名称是否有效（如果提供了的话）
        if name and (len(name) > 50 or '%' in name or '_' in name):
            logger.warning(f"获取分享配置时收到无效的名称参数: '{name}'")
            return jsonify({
                'success': False,
                'error': '无效的名称参数'
            }), 400
        
        # 创建不同页面的分享配置
        # 可以从数据库中获取，这里为简单起见使用静态配置
        share_configs = {
            'index': {
                'title': '✨一封穿越人海的神秘信件，请查收',
                'imageUrl': '/static/share/index1.png'
            },
            'write': {
                'title': '有人偷偷给你写了句话…会是谁呢？',
                'imageUrl': '/static/share/write1.png'
            },
            'view': {
                'title': '{name}在等你的悄悄话，TA会看到第一个哦…',
                'imageUrl': '/static/share/view1.png'
            },
            'my_messages': {
                'title': '给你留了点东西，记得去看看~',
                'imageUrl': '/static/share/my_messages1.png'
            }
        }
        
        # 获取当前页面的配置
        config = share_configs.get(page_key, share_configs['index'])
        logger.debug(f"应用默认配置 - 页面: {page_key}, 标题: '{config['title']}', 图片URL: '{config['imageUrl']}'")
        
        # 如果是需要替换名称的页面，并且提供了名称参数
        if page_key == 'view' and name:
            # 将标题中的 {name} 替换为实际名称
            original_title = config['title']
            config['title'] = config['title'].replace('{name}', name)
            logger.debug(f"替换标题占位符 - 原始标题: '{original_title}' -> 替换后: '{config['title']}'")
        
        # 添加绝对URL处理 (如果需要)
        image_url = config['imageUrl']
        if image_url.startswith('/'):
            # 可以添加完整的基础URL，如果需要的话
            # image_url = f"https://your-domain.com{image_url}"
            logger.debug(f"图片URL是相对路径: '{image_url}'，小程序会自动处理")
        
        response_data = {
            'success': True,
            'title': config['title'],
            'imageUrl': config['imageUrl']
        }
        
        logger.debug(f"返回分享配置 - 页面: {page_key}, 响应数据: {response_data}")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"获取分享配置失败: {str(e)}", exc_info=True)  # 保留错误日志
        return jsonify({
            'success': False,
            'error': '获取分享配置失败，请稍后重试'
        }), 500


@app.route('/mailbox/total-messages', methods=['GET'])
@token_required  # 保留JWT认证，虽然不直接用用户信息，但可能作为访问控制
def get_total_messages():
    """获取总留言数"""
    try:
        # 可以选择性记录访问用户
        # current_user = request.current_user
        # user_id = current_user.get('id')
        # logger.info(f"用户 {user_id if user_id else '未认证'} 请求总留言数")
        
        connection = get_mailbox_db_connection()
        try:
            with connection.cursor() as cursor:
                # 查询总留言数
                cursor.execute("SELECT COUNT(*) as total FROM anonymous_messages")
                total = cursor.fetchone()['total']
                logger.info(f"数据库中总留言数: {total}")

                return jsonify({
                    'success': True,
                    'total': total
                })

        except pymysql.Error as db_error:
            return handle_db_error(db_error, "获取总留言数")
        finally:
            if connection:
                connection.close()

    except KeyError: # 捕获 current_user['id'] 不存在的情况 (如果未来需要严格依赖用户ID)
        logger.error("Token 验证通过，但未能获取用户 ID (可能是 jwt_auth 模块数据库错误)")
        return jsonify({'error': '用户认证信息不完整，请稍后重试'}), 500
    except Exception as e:
        logger.error(f"获取总留言数失败：{str(e)}")
        return jsonify({'success': False, 'error': '获取留言数失败，请稍后重试'}), 500


@app.route('/mailbox/messages/<int:message_id>/likes', methods=['GET'])
@token_required
def get_message_likes(message_id):
    """获取指定留言的点赞用户列表"""
    try:
        current_user = request.current_user
        user_id = current_user['id']

        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('pageSize', 20, type=int)

        # 限制分页参数范围
        page = max(1, page)
        page_size = min(max(1, page_size), 50)  # 最大50条
        offset = (page - 1) * page_size

        connection = get_mailbox_db_connection()
        try:
            with connection.cursor() as cursor:
                # 首先验证留言是否存在，并检查当前用户是否为作者
                cursor.execute(
                    "SELECT id, user_id, name, content, like_count FROM anonymous_messages WHERE id = %s",
                    (message_id,)
                )
                message = cursor.fetchone()

                if not message:
                    return jsonify({'error': '留言不存在'}), 404

                # 验证当前用户是否为留言作者
                if message['user_id'] != user_id:
                    return jsonify({'error': '无权查看此留言的点赞信息'}), 403

                # 查询点赞用户列表（分页）
                cursor.execute("""
                    SELECT
                        ml.created_at as like_time,
                        u.nickname,
                        u.avatar_url,
                        u.platform
                    FROM message_likes ml
                    JOIN users u ON ml.user_id = u.id
                    WHERE ml.message_id = %s
                    ORDER BY ml.created_at DESC
                    LIMIT %s OFFSET %s
                """, (message_id, page_size, offset))

                likes = cursor.fetchall()

                # 查询总点赞数
                cursor.execute(
                    "SELECT COUNT(*) as total FROM message_likes WHERE message_id = %s",
                    (message_id,)
                )
                total_count = cursor.fetchone()['total']

                # 格式化时间
                for like in likes:
                    like['like_time'] = format_datetime(like.get('like_time'))

                # 构建响应
                response_data = {
                    'success': True,
                    'data': {
                        'message_info': {
                            'id': message['id'],
                            'name': message['name'],
                            'content': message['content'][:50] + ('...' if len(message['content']) > 50 else ''),
                            'like_count': message['like_count']
                        },
                        'likes': likes,
                        'pagination': {
                            'page': page,
                            'pageSize': page_size,
                            'total': total_count,
                            'totalPages': (total_count + page_size - 1) // page_size,
                            'hasMore': (page * page_size) < total_count
                        }
                    }
                }

                logger.info(f"用户 {user_id} 查看留言 {message_id} 的点赞列表，返回 {len(likes)} 条记录")
                return jsonify(response_data)

        except pymysql.Error as db_error:
            return handle_db_error(db_error, "获取点赞列表")
        finally:
            if connection:
                connection.close()

    except Exception as e:
        logger.error(f"处理获取点赞列表请求时发生未知错误: {str(e)}")
        return jsonify({'error': '获取点赞列表失败'}), 500


@app.route('/mailbox/feedback', methods=['POST'])
@token_required
def submit_feedback():
    """接收用户反馈并保存到文本文件"""
    try:
        # 获取用户ID和平台信息
        current_user = request.current_user
        user_id = current_user.get('id', 'unknown')
        platform = current_user.get('platform', 'unknown')
        
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400
        
        # 验证必要字段
        required_fields = ['qq', 'content']
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            return jsonify({'error': f'缺少必要字段：{", ".join(missing_fields)}'}), 400
        
        # 提取数据
        qq = data['qq']
        content = data['content']
        timestamp = data.get('timestamp', datetime.datetime.now().isoformat())
        
        # 简单的内容安全检查 (长度限制)
        if len(content) > 1000:
            return jsonify({'error': '反馈内容过长，请缩短后重试'}), 400
        
        # 构建反馈记录
        feedback_record = {
            'user_id': user_id,
            'platform': platform,
            'qq': qq,
            'content': content,
            'timestamp': timestamp
        }
        
        # 创建反馈文件名 (按日期存储)
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        # --- 确保 feedback 目录存在 ---
        feedback_dir = os.path.join(static_folder, 'feedback')
        if not os.path.exists(feedback_dir):
            try:
                os.makedirs(feedback_dir)
                logger.info(f"已创建 feedback 目录: {feedback_dir}")
            except Exception as e:
                logger.error(f"创建 feedback 目录失败: {str(e)}")
                # 如果目录创建失败，后续写入也会失败，这里提前返回错误
                return jsonify({'error': '创建反馈目录失败，请联系管理员'}), 500
        # --- 目录检查结束 ---
        feedback_file = os.path.join(feedback_dir, f'feedback_{today}.txt')
        
        # 将反馈写入文件
        try:
            with open(feedback_file, 'a', encoding='utf-8') as f:
                # 改为格式化输出，而不是一行JSON
                f.write(f"===== 反馈记录 [{timestamp}] =====\n")
                f.write(f"用户ID: {user_id}\n")
                f.write(f"平台: {platform}\n")
                f.write(f"QQ号: {qq}\n")
                f.write(f"反馈内容:\n{content}\n")
                f.write("="*40 + "\n\n") # 分隔线
            
            logger.info(f"用户 {user_id} ({platform}) 提交了反馈，已保存到 {feedback_file}")
            return jsonify({'success': True, 'message': '感谢您的反馈！'})
        except Exception as write_err:
            logger.error(f"保存反馈到文件失败: {str(write_err)}")
            return jsonify({'error': '保存反馈失败，请稍后重试'}), 500
            
    except KeyError:
        logger.error("Token 验证通过，但未能获取用户 ID")
        return jsonify({'error': '用户认证信息不完整，请稍后重试'}), 500
    except Exception as e:
        logger.error(f"处理反馈请求时发生未知错误: {str(e)}")
        return jsonify({'error': '处理反馈请求失败'}), 500


@app.route('/mailbox/user-notice', methods=['GET'])
@token_required
def get_user_notice():
    """获取针对特定用户的通知信息"""
    try:
        # 从上下文中获取用户信息
        current_user = request.current_user
        user_id = current_user.get('id')
        platform = current_user.get('platform')
        
        # 检查通知开关
        if not USER_NOTICE_CONFIG["notice"]["show"]:
            logger.debug(f"用户通知功能已关闭，用户ID: {user_id}")
            return jsonify({
                "success": False,
                "message": "用户通知功能未启用"
            })
        
        # 查询用户的remark
        connection = get_mailbox_db_connection()
        try:
            with connection.cursor() as cursor:
                cursor.execute(
                    "SELECT remark FROM users WHERE id = %s",
                    (user_id,)
                )
                user_info = cursor.fetchone()
                
                if not user_info or not user_info['remark']:
                    logger.debug(f"用户 {user_id} 没有备注信息")
                    return jsonify({
                        "success": False,
                        "message": "无通知"
                    })
                
                # 检查备注是否匹配触发条件
                user_remark = user_info['remark']
                if user_remark == USER_NOTICE_CONFIG["trigger_remark"]:
                    logger.info(f"用户 {user_id} (Platform: {platform}) 的备注 '{user_remark}' 触发了个人通知")
                    
                    # 返回通知信息
                    return jsonify({
                        "success": True,
                        "title": USER_NOTICE_CONFIG["notice"]["title"],
                        "body": USER_NOTICE_CONFIG["notice"]["body"],
                        "imageUrl": USER_NOTICE_CONFIG["notice"]["image_path"],
                        "id": USER_NOTICE_CONFIG["notice"]["id"]
                    })
                else:
                    logger.debug(f"用户 {user_id} 的备注 '{user_remark}' 不符合通知条件")
                    return jsonify({
                        "success": False,
                        "message": "备注不符合通知条件"
                    })
                
        except pymysql.Error as db_error:
            return handle_db_error(db_error, "查询用户通知信息")
        finally:
            if connection:
                connection.close()
                
    except KeyError:
        logger.error("Token 验证通过，但未能获取用户 ID")
        return jsonify({
            'success': False,
            'error': '用户认证信息不完整，请稍后重试'
        }), 500
    except Exception as e:
        logger.error(f"获取用户通知失败：{str(e)}")
        return jsonify({
            'success': False,
            'error': '获取用户通知失败，请稍后重试'
        }), 500

@app.route('/mailbox/debug/headers', methods=['GET', 'POST'])
def debug_request_headers():
    """调试用：返回请求头，用于验证小程序请求是否包含 AppID 等信息（上线前可移除）"""
    headers_dict = {k: v for k, v in request.headers.items()}
    logger.debug(f"调试端点 /mailbox/debug/headers 收到请求头: {headers_dict}")
    return jsonify({'success': True, 'headers': headers_dict})

# === 新增：默认昵称处理 ===
# 当昵称属于占位值时，统一替换为"留言用户"，并使用默认头像（留空让前端走默认）
DEFAULT_NICKNAME_PLACEHOLDERS = {"没改名的微信用户", "没改名的QQ用户", "未获取昵称"}
DEFAULT_NICKNAME = "留言用户"

def sanitize_author_info(nickname, avatar_url):
    """根据昵称是否为占位值，返回处理后的作者信息字典"""
    if not nickname or nickname in DEFAULT_NICKNAME_PLACEHOLDERS:
        return {
            'nickname': DEFAULT_NICKNAME,
            'avatarUrl': ''  # 让前端走默认头像
        }
    return {
        'nickname': nickname,
        'avatarUrl': avatar_url or ''
    }
# === 默认昵称处理结束 ===

if __name__ == '__main__':
    # 生产环境部署时建议使用 gunicorn 等 WSGI 服务器
    # 确保在应用退出时关闭连接池 (虽然 gunicorn 等通常会处理进程退出)
    try:
        # 注意：移除了 app.run 中的 debug=True，因为 Flask 在 debug 模式下会启动两次，
        # 这会导致启动函数执行两次。如果需要 debug，请暂时注释掉 send_test_subscribe_message_on_startup() 调用。
        # 为简单起见，暂时移除 debug=True。
        # 运行 Flask 开发服务器 (仅用于调试)
        # 注意：现在连接池在connection_pools模块中统一管理
        app.run(host='0.0.0.0', port=5000) # 监听所有地址方便测试
    finally:
        # 使用集中管理的函数关闭所有连接池
        close_pools()
        logger.info("应用程序退出，尝试关闭所有数据库连接池")