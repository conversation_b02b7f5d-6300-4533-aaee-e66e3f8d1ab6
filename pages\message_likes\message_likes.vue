<template>
	<view class="likes-detail-container">
		<!-- 装饰性背景元素 -->
		<view class="decorative-circle circle-1"></view>
		<view class="decorative-circle circle-2"></view>
		<view class="decorative-shape shape-1"></view>
		<view class="decorative-shape shape-2"></view>

		<!-- 留言信息卡片 -->
		<view class="message-summary-card">
			<view class="summary-header">
				<text class="recipient-info">给 {{messageInfo.name}} 的留言</text>
				<view class="likes-badge">
					<view class="heart-icon"></view>
					<text class="total-likes">{{messageInfo.like_count}} 人点赞</text>
				</view>
			</view>
			<text class="message-preview">{{messageInfo.content}}</text>
		</view>

		<!-- 点赞用户列表 -->
		<view class="likes-list-section">
			<view class="section-title">
				<text class="title-text">点赞用户</text>
				<text class="subtitle-text">按时间排序</text>
			</view>

			<!-- 加载状态 -->
			<view v-if="isLoading && likes.length === 0" class="loading-state">
				<uni-load-more status="loading" :showText="false"></uni-load-more>
			</view>

			<!-- 点赞列表 -->
			<scroll-view
				v-else-if="likes.length > 0"
				class="likes-scroll"
				scroll-y
				@scrolltolower="loadMoreLikes"
				:scroll-with-animation="true"
				:show-scrollbar="false"
			>
				<view class="likes-list">
					<view
						class="like-item"
						v-for="(like, index) in likes"
						:key="index"
						:style="{ animationDelay: `${index * 0.05}s` }"
					>
						<image class="user-avatar" :src="formatAvatarUrl(like.avatar_url)" mode="aspectFill" />
						<view class="user-info">
							<text class="nickname">{{like.nickname}}</text>
							<text class="like-time">{{like.like_time}}</text>
						</view>
						<view class="platform-badge" :class="like.platform.toLowerCase()">
							<text class="platform-text">{{like.platform === 'WX' ? '微信' : 'QQ'}}</text>
						</view>
					</view>
				</view>

				<!-- 加载更多提示 -->
				<uni-load-more :status="loadingStatus"></uni-load-more>
			</scroll-view>

			<!-- 空状态 -->
			<view v-else-if="!isLoading" class="empty-likes">
				<view class="empty-illustration"></view>
				<text class="empty-text">还没有人点赞这条留言</text>
				<text class="empty-hint">分享给朋友，让更多人看到吧～</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				messageId: null,
				messageInfo: {
					name: '',
					content: '',
					like_count: 0
				},
				likes: [],
				isLoading: false,
				token: null,
				page: 1,
				pageSize: 20,
				hasMore: true,
				loadingStatus: 'more' // more, loading, noMore
			}
		},
		onLoad(options) {
			this.messageId = options.messageId;
			this.token = uni.getStorageSync('mailbox_token');

			if (!this.messageId) {
				uni.showToast({ title: '参数错误', icon: 'none' });
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
				return;
			}

			this.fetchLikes(true);
		},
		methods: {
			// 获取点赞列表
			async fetchLikes(refresh = false) {
				if (this.isLoading || this.loadingStatus === 'loading') return;

				if (refresh) {
					this.page = 1;
					this.likes = [];
					this.hasMore = true;
					this.loadingStatus = 'more';
				} else if (!this.hasMore) {
					this.loadingStatus = 'noMore';
					return;
				}

				this.isLoading = this.likes.length === 0;
				this.loadingStatus = 'loading';

				try {
					await this.ensureToken();
					if (!this.token) throw new Error('Token is missing');

					const res = await uni.request({
						url: `${this.$baseUrl}/mailbox/messages/${this.messageId}/likes?page=${this.page}&pageSize=${this.pageSize}`,
						method: 'GET',
						header: { 'Authorization': `Bearer ${this.token}` }
					});

					if (res.statusCode === 200 && res.data.success) {
						const { message_info, likes, pagination } = res.data.data;

						// 设置留言信息
						if (refresh) {
							this.messageInfo = message_info;
						}

						// 更新点赞列表
						if (refresh) {
							this.likes = likes;
						} else {
							this.likes = [...this.likes, ...likes];
						}

						// 更新分页状态
						this.hasMore = pagination.hasMore;
						this.page = pagination.page + 1;
						this.loadingStatus = this.hasMore ? 'more' : 'noMore';

					} else if (res.statusCode === 401) {
						const success = await this.handleTokenExpired();
						if (success) {
							await this.fetchLikes(refresh);
						}
					} else if (res.statusCode === 403) {
						uni.showToast({ title: '无权查看此留言的点赞信息', icon: 'none' });
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					} else if (res.statusCode === 404) {
						uni.showToast({ title: '留言不存在', icon: 'none' });
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					} else {
						throw new Error(`API Error: ${res.statusCode}`);
					}
				} catch (error) {
					console.error('获取点赞列表失败:', error);
					this.loadingStatus = 'more';
					uni.showToast({ title: '加载失败，请重试', icon: 'none' });
				} finally {
					this.isLoading = false;
					if (this.loadingStatus === 'loading') {
						this.loadingStatus = this.hasMore ? 'more' : 'noMore';
					}
				}
			},

			// 加载更多
			loadMoreLikes() {
				this.fetchLikes();
			},

			// 确保token有效
			async ensureToken() {
				if (!this.token) {
					this.token = uni.getStorageSync('mailbox_token');
				}
			},

			// 处理token过期
			async handleTokenExpired() {
				uni.showToast({ title: '登录信息已过期，请返回首页重试', icon: 'none' });
				setTimeout(() => {
					uni.switchTab({ url: '/pages/index/index' });
				}, 1500);
				return false;
			},

			// 格式化头像URL
			formatAvatarUrl(url) {
				if (!url) return '/static/default-avatar.png';
				if (url.startsWith('http')) return url;
				return `${this.$baseUrl}${url}`;
			}
		}
	}
</script>

<style>
/* 基础样式 */
.likes-detail-container {
	min-height: 100vh;
	background: linear-gradient(180deg, #fdfbfb 0%, #f6f7fb 100%);
	padding: 0;
	position: relative;
	overflow: hidden;
	padding-top: var(--status-bar-height);
}

/* 装饰元素 */
.decorative-circle {
	position: absolute;
	border-radius: 50%;
	z-index: 0;
}

.circle-1 {
	width: 300rpx;
	height: 300rpx;
	background: linear-gradient(135deg, rgba(255, 107, 145, 0.6), rgba(255, 158, 158, 0.4));
	top: -150rpx;
	right: -50rpx;
	filter: blur(25rpx);
	animation: floatAnimation 6s ease-in-out infinite;
}

.circle-2 {
	width: 200rpx;
	height: 200rpx;
	background: linear-gradient(135deg, rgba(79, 95, 232, 0.5), rgba(255, 107, 145, 0.3));
	bottom: 100rpx;
	left: -50rpx;
	filter: blur(20rpx);
	animation: floatAnimation 8s ease-in-out infinite reverse;
}

.decorative-shape {
	position: absolute;
	z-index: 0;
}

.shape-1 {
	width: 100rpx;
	height: 100rpx;
	background: rgba(79, 95, 232, 0.1);
	border-radius: 20rpx;
	top: 200rpx;
	left: 50rpx;
	transform: rotate(45deg);
	animation: rotateAnimation 10s linear infinite;
}

.shape-2 {
	width: 80rpx;
	height: 80rpx;
	background: rgba(255, 107, 145, 0.1);
	border-radius: 50%;
	bottom: 300rpx;
	right: 80rpx;
	animation: floatAnimation 7s ease-in-out infinite;
}

@keyframes floatAnimation {
	0%, 100% { transform: translateY(0) rotate(0deg); }
	50% { transform: translateY(-20rpx) rotate(5deg); }
}

@keyframes rotateAnimation {
	from { transform: rotate(45deg); }
	to { transform: rotate(405deg); }
}

/* 留言信息卡片 */
.message-summary-card {
	margin: 30rpx;
	padding: 30rpx;
	background: rgba(255, 255, 255, 0.8);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-radius: 24rpx;
	box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.05);
	border: 1rpx solid rgba(255, 255, 255, 0.6);
	position: relative;
	z-index: 5;
}

.summary-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.recipient-info {
	font-size: 28rpx;
	font-weight: 600;
	color: #4f5fe8;
}

.likes-badge {
	display: flex;
	align-items: center;
	background: linear-gradient(135deg, rgba(255, 107, 145, 0.1), rgba(79, 95, 232, 0.1));
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	border: 1rpx solid rgba(255, 107, 145, 0.2);
}

.heart-icon {
	width: 24rpx;
	height: 24rpx;
	background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ff6b91'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E") no-repeat center;
	background-size: contain;
	margin-right: 8rpx;
}

.total-likes {
	font-size: 22rpx;
	color: #ff6b91;
	font-weight: 600;
}

.message-preview {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
	background: rgba(255, 255, 255, 0.5);
	padding: 16rpx;
	border-radius: 12rpx;
	border-left: 4rpx solid #f0f0f0;
}

/* 点赞列表区域 */
.likes-list-section {
	margin: 0 30rpx;
	position: relative;
	z-index: 5;
	flex: 1;
}

.section-title {
	margin-bottom: 20rpx;
}

.title-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-right: 16rpx;
}

.subtitle-text {
	font-size: 22rpx;
	color: #999;
}

/* 滚动区域 */
.likes-scroll {
	height: calc(100vh - 400rpx);
}

.likes-list {
	padding-bottom: 120rpx;
}

/* 点赞项样式 */
.like-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	margin-bottom: 16rpx;
	background: rgba(255, 255, 255, 0.7);
	backdrop-filter: blur(15rpx);
	-webkit-backdrop-filter: blur(15rpx);
	border-radius: 16rpx;
	box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.03);
	border: 1rpx solid rgba(255, 255, 255, 0.5);
	animation: itemFadeIn 0.5s ease forwards;
	opacity: 0;
	transform: translateY(10rpx);
}

@keyframes itemFadeIn {
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.user-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 20rpx;
	border: 2rpx solid rgba(255, 255, 255, 0.8);
}

.user-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.nickname {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 6rpx;
}

.like-time {
	font-size: 22rpx;
	color: #999;
}

.platform-badge {
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
}

.platform-badge.wx {
	background: rgba(7, 193, 96, 0.1);
	color: #07c160;
}

.platform-badge.qq {
	background: rgba(18, 183, 245, 0.1);
	color: #12b7f5;
}

.platform-text {
	font-weight: 500;
}

/* 加载状态 */
.loading-state {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 200rpx;
}

/* 空状态 */
.empty-likes {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 40rpx;
	text-align: center;
}

.empty-illustration {
	width: 200rpx;
	height: 200rpx;
	background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 200'%3E%3Ccircle cx='100' cy='100' r='80' fill='%23f0f0f0'/%3E%3Cpath d='M70 85c0-5 5-10 10-10s10 5 10 10-5 10-10 10-10-5-10-10zm40 0c0-5 5-10 10-10s10 5 10 10-5 10-10 10-10-5-10-10zm-30 40c10 10 20 10 30 0' stroke='%23ccc' stroke-width='3' fill='none'/%3E%3C/svg%3E") no-repeat center;
	background-size: contain;
	margin-bottom: 30rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 12rpx;
}

.empty-hint {
	font-size: 24rpx;
	color: #999;
}
</style>
