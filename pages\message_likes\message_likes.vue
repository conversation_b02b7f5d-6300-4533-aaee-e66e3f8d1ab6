<template>
	<view class="likes-detail-container">
		<!-- 装饰性背景元素 -->
		<view class="decorative-circle circle-1"></view>
		<view class="decorative-circle circle-2"></view>
		<view class="decorative-shape shape-1"></view>
		<view class="decorative-shape shape-2"></view>
		<view class="decorative-pattern"></view>

		<!-- 顶部导航区域 -->
		<view class="header-section">
			<view class="back-button" @tap="goBack">
				<view class="back-icon"></view>
				<text class="back-text">返回</text>
			</view>
			<text class="page-title">点赞详情</text>
			<view class="header-placeholder"></view>
		</view>

		<!-- 留言信息卡片 -->
		<view class="message-summary-card">
			<view class="summary-header">
				<view class="recipient-info">
					<view class="recipient-icon"></view>
					<text class="recipient-text">给 {{messageName}} 的留言</text>
				</view>
				<view class="likes-badge">
					<view class="heart-icon"></view>
					<text class="total-likes">{{totalLikes}} 人点赞</text>
				</view>
			</view>
			<view class="message-preview-wrapper">
				<text class="message-preview">{{messageContent}}</text>
			</view>
		</view>

		<!-- 点赞用户列表区域 -->
		<view class="likes-list-section">
			<view class="section-header">
				<view class="section-title-group">
					<view class="section-title-decoration"></view>
					<text class="section-title">点赞用户</text>
				</view>
				<text class="section-subtitle">按时间排序</text>
			</view>

			<!-- 加载状态 -->
			<view v-if="isLoading" class="loading-state">
				<view class="skeleton-item" v-for="i in 3" :key="i">
					<view class="skeleton-avatar skeleton-shimmer"></view>
					<view class="skeleton-content">
						<view class="skeleton-name skeleton-shimmer"></view>
						<view class="skeleton-time skeleton-shimmer"></view>
					</view>
				</view>
			</view>

			<!-- 点赞列表 -->
			<scroll-view
				v-else-if="likes.length > 0"
				class="likes-scroll"
				scroll-y
				:show-scrollbar="false"
				@scrolltolower="loadMore"
			>
				<view class="likes-list">
					<view
						class="like-item"
						v-for="(like, index) in likes"
						:key="like.id"
						:class="{ 'item-appear': showAnimation }"
						:style="{ animationDelay: `${index * 0.1}s` }"
					>
						<view class="user-avatar-wrapper">
							<image class="user-avatar" :src="like.avatar_url" mode="aspectFill"></image>
							<view class="avatar-ring"></view>
						</view>
						<view class="user-info">
							<text class="nickname">{{like.nickname}}</text>
							<text class="like-time">{{formatTime(like.like_time)}}</text>
						</view>
						<view class="platform-badge" :class="like.platform.toLowerCase()">
							<view class="platform-icon" :class="like.platform.toLowerCase()"></view>
							<text class="platform-text">{{like.platform === 'WX' ? '微信' : 'QQ'}}</text>
						</view>
					</view>
				</view>

				<!-- 加载更多提示 -->
				<view class="load-more-wrapper" v-if="hasMore">
					<view class="load-more-indicator">
						<view class="loading-dots">
							<view class="dot"></view>
							<view class="dot"></view>
							<view class="dot"></view>
						</view>
						<text class="load-more-text">加载更多...</text>
					</view>
				</view>

				<!-- 没有更多提示 -->
				<view class="no-more-wrapper" v-else-if="likes.length > 0">
					<view class="no-more-line"></view>
					<text class="no-more-text">没有更多了</text>
					<view class="no-more-line"></view>
				</view>
			</scroll-view>

			<!-- 空状态 -->
			<view v-else class="empty-likes">
				<view class="empty-illustration">
					<view class="empty-heart"></view>
					<view class="empty-sparkles">
						<view class="sparkle sparkle-1"></view>
						<view class="sparkle sparkle-2"></view>
						<view class="sparkle sparkle-3"></view>
					</view>
				</view>
				<text class="empty-title">还没有人点赞</text>
				<text class="empty-subtitle">分享给朋友，让更多人看到你的留言吧～</text>
				<view class="empty-action" @tap="shareMessage">
					<view class="share-icon"></view>
					<text class="share-text">分享留言</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			messageId: '',
			messageName: '',
			messageContent: '',
			totalLikes: 0,
			likes: [],
			isLoading: false,
			showAnimation: false,
			hasMore: true,
			page: 1,
			pageSize: 20
		};
	},

	onLoad(options) {
		// 获取传递的参数
		this.messageId = options.messageId || '';
		this.messageName = decodeURIComponent(options.messageName || '');
		this.messageContent = decodeURIComponent(options.messageContent || '这是一条温暖的留言内容...');

		// 模拟数据加载
		this.loadLikesData();

		// 设置页面标题
		uni.setNavigationBarTitle({
			title: '点赞详情'
		});
	},

	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 加载点赞数据（模拟）
		loadLikesData() {
			this.isLoading = true;

			// 模拟网络延迟
			setTimeout(() => {
				// 模拟点赞数据
				const mockLikes = [
					{
						id: 1,
						nickname: '小明同学',
						avatar_url: '/static/default-avatar.png',
						like_time: '2024-01-15 14:30:00',
						platform: 'WX'
					},
					{
						id: 2,
						nickname: '温柔的月亮',
						avatar_url: '/static/default-avatar.png',
						like_time: '2024-01-15 12:15:00',
						platform: 'QQ'
					},
					{
						id: 3,
						nickname: '星空下的梦',
						avatar_url: '/static/default-avatar.png',
						like_time: '2024-01-14 20:45:00',
						platform: 'WX'
					},
					{
						id: 4,
						nickname: '阳光少年',
						avatar_url: '/static/default-avatar.png',
						like_time: '2024-01-14 16:20:00',
						platform: 'QQ'
					},
					{
						id: 5,
						nickname: '花开半夏',
						avatar_url: '/static/default-avatar.png',
						like_time: '2024-01-14 10:30:00',
						platform: 'WX'
					},
					{
						id: 6,
						nickname: '追风少年',
						avatar_url: '/static/default-avatar.png',
						like_time: '2024-01-13 18:45:00',
						platform: 'QQ'
					},
					{
						id: 7,
						nickname: '夜空中最亮的星',
						avatar_url: '/static/default-avatar.png',
						like_time: '2024-01-13 15:20:00',
						platform: 'WX'
					},
					{
						id: 8,
						nickname: '微风轻语',
						avatar_url: '/static/default-avatar.png',
						like_time: '2024-01-12 22:10:00',
						platform: 'QQ'
					}
				];

				this.likes = mockLikes;
				this.totalLikes = mockLikes.length;
				this.isLoading = false;
				this.hasMore = false;

				// 触发动画
				this.$nextTick(() => {
					this.showAnimation = true;
				});
			}, 1000);
		},

		// 加载更多
		loadMore() {
			if (this.hasMore && !this.isLoading) {
				// 这里可以实现分页加载逻辑
				console.log('加载更多点赞数据');
			}
		},

		// 分享留言
		shareMessage() {
			uni.showToast({
				title: '分享功能开发中',
				icon: 'none'
			});
		},

		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return '';

			const time = new Date(timeStr);
			const now = new Date();
			const diff = now - time;

			// 小于1分钟
			if (diff < 60000) {
				return '刚刚';
			}
			// 小于1小时
			if (diff < 3600000) {
				return Math.floor(diff / 60000) + '分钟前';
			}
			// 小于1天
			if (diff < 86400000) {
				return Math.floor(diff / 3600000) + '小时前';
			}
			// 小于7天
			if (diff < 604800000) {
				return Math.floor(diff / 86400000) + '天前';
			}

			// 超过7天显示具体日期
			const month = time.getMonth() + 1;
			const day = time.getDate();
			const hour = time.getHours();
			const minute = time.getMinutes();

			return `${month}月${day}日 ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
		}
	}
};
</script>

<style>
/* 基础容器样式 */
.likes-detail-container {
	min-height: 100vh;
	background: linear-gradient(180deg, #fdfbfb 0%, #f6f7fb 100%);
	padding-top: var(--status-bar-height);
	position: relative;
	overflow: hidden;
}

/* 装饰性背景元素 */
.decorative-circle {
	position: absolute;
	border-radius: 50%;
	z-index: 0;
}

.circle-1 {
	width: 400rpx;
	height: 400rpx;
	background: linear-gradient(135deg, rgba(255, 207, 179, 0.7), rgba(255, 158, 158, 0.5));
	top: -200rpx;
	right: -100rpx;
	filter: blur(30rpx);
	animation: floatAnimation 8s ease-in-out infinite;
}

.circle-2 {
	width: 300rpx;
	height: 300rpx;
	background: linear-gradient(135deg, rgba(79, 95, 232, 0.6), rgba(255, 107, 145, 0.4));
	bottom: -150rpx;
	left: -80rpx;
	filter: blur(25rpx);
	animation: floatAnimation 10s ease-in-out infinite reverse;
}

.decorative-shape {
	position: absolute;
	z-index: 0;
}

.shape-1 {
	width: 200rpx;
	height: 200rpx;
	background: linear-gradient(45deg, rgba(255, 107, 145, 0.3), rgba(79, 95, 232, 0.2));
	border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
	top: 200rpx;
	left: -50rpx;
	filter: blur(20rpx);
	animation: morphAnimation 12s ease-in-out infinite;
}

.shape-2 {
	width: 150rpx;
	height: 150rpx;
	background: linear-gradient(135deg, rgba(255, 158, 158, 0.4), rgba(255, 207, 179, 0.3));
	border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
	bottom: 300rpx;
	right: -30rpx;
	filter: blur(15rpx);
	animation: morphAnimation 15s ease-in-out infinite reverse;
}

.decorative-pattern {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 600rpx;
	height: 600rpx;
	background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1rpx, transparent 1rpx);
	background-size: 40rpx 40rpx;
	opacity: 0.3;
	z-index: 0;
	animation: patternRotate 30s linear infinite;
}

/* 动画定义 */
@keyframes floatAnimation {
	0%, 100% { transform: translateY(0) rotate(0deg); }
	50% { transform: translateY(-20rpx) rotate(5deg); }
}

@keyframes morphAnimation {
	0%, 100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
	25% { border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; }
	50% { border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%; }
	75% { border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%; }
}

@keyframes patternRotate {
	from { transform: translate(-50%, -50%) rotate(0deg); }
	to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 顶部导航区域 */
.header-section {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	position: relative;
	z-index: 10;
}

.back-button {
	display: flex;
	align-items: center;
	padding: 12rpx 20rpx;
	background: rgba(255, 255, 255, 0.8);
	backdrop-filter: blur(20rpx);
	border-radius: 25rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.6);
	transition: all 0.3s ease;
}

.back-button:active {
	transform: scale(0.95);
	background: rgba(255, 255, 255, 0.9);
}

.back-icon {
	width: 24rpx;
	height: 24rpx;
	background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234f5fe8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='15 18 9 12 15 6'%3E%3C/polyline%3E%3C/svg%3E") no-repeat center;
	background-size: contain;
	margin-right: 8rpx;
}

.back-text {
	font-size: 28rpx;
	color: #4f5fe8;
	font-weight: 500;
}

.page-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	text-align: center;
}

.header-placeholder {
	width: 120rpx;
}

/* 留言信息卡片 */
.message-summary-card {
	margin: 20rpx 30rpx;
	padding: 30rpx;
	background: rgba(255, 255, 255, 0.8);
	backdrop-filter: blur(20rpx);
	border-radius: 24rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.6);
	box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.05);
	position: relative;
	z-index: 5;
	overflow: hidden;
}

.message-summary-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 6rpx;
	background: linear-gradient(90deg, #ff6f91, #4f5fe8);
	opacity: 0.8;
}

.summary-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.recipient-info {
	display: flex;
	align-items: center;
}

.recipient-icon {
	width: 28rpx;
	height: 28rpx;
	background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234f5fe8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z'%3E%3C/path%3E%3Cpolyline points='22 6 12 13 2 6'%3E%3C/polyline%3E%3C/svg%3E") no-repeat center;
	background-size: contain;
	margin-right: 10rpx;
}

.recipient-text {
	font-size: 28rpx;
	color: #4f5fe8;
	font-weight: 600;
}

.likes-badge {
	display: flex;
	align-items: center;
	background: linear-gradient(135deg, rgba(255, 107, 145, 0.1), rgba(79, 95, 232, 0.1));
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	border: 1rpx solid rgba(255, 107, 145, 0.2);
}

.heart-icon {
	width: 24rpx;
	height: 24rpx;
	background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ff6b91' stroke='%23ff6b91' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E") no-repeat center;
	background-size: contain;
	margin-right: 8rpx;
	animation: heartBeat 2s ease-in-out infinite;
}

.total-likes {
	font-size: 24rpx;
	color: #ff6b91;
	font-weight: 600;
}

@keyframes heartBeat {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.1); }
}

.message-preview-wrapper {
	background: rgba(255, 255, 255, 0.5);
	padding: 20rpx;
	border-radius: 16rpx;
	border-left: 4rpx solid #f0f0f0;
}

.message-preview {
	font-size: 28rpx;
	color: #444;
	line-height: 1.6;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3;
	line-clamp: 3;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* 点赞列表区域 */
.likes-list-section {
	margin: 20rpx 30rpx;
	position: relative;
	z-index: 5;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.section-title-group {
	display: flex;
	align-items: center;
}

.section-title-decoration {
	width: 6rpx;
	height: 32rpx;
	background: linear-gradient(180deg, #ff6b91, #4f5fe8);
	border-radius: 3rpx;
	margin-right: 12rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.section-subtitle {
	font-size: 24rpx;
	color: #888;
	background: rgba(0, 0, 0, 0.03);
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
}

/* 加载状态 */
.loading-state {
	padding: 20rpx 0;
}

.skeleton-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	margin-bottom: 15rpx;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 16rpx;
}

.skeleton-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 20rpx;
}

.skeleton-content {
	flex: 1;
}

.skeleton-name {
	width: 200rpx;
	height: 28rpx;
	border-radius: 14rpx;
	margin-bottom: 10rpx;
}

.skeleton-time {
	width: 120rpx;
	height: 20rpx;
	border-radius: 10rpx;
}

.skeleton-shimmer {
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
	0% { background-position: -200% 0; }
	100% { background-position: 200% 0; }
}

/* 点赞列表滚动区域 */
.likes-scroll {
	max-height: 800rpx;
}

.likes-list {
	padding-bottom: 20rpx;
}

/* 点赞项样式 */
.like-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	margin-bottom: 15rpx;
	background: rgba(255, 255, 255, 0.8);
	backdrop-filter: blur(20rpx);
	border-radius: 20rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.6);
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.03);
	transition: all 0.3s ease;
	opacity: 0;
	transform: translateY(20rpx);
}

.like-item.item-appear {
	animation: itemFadeIn 0.5s ease forwards;
}

@keyframes itemFadeIn {
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.like-item:active {
	transform: scale(0.98);
	background: rgba(255, 255, 255, 0.9);
}

.user-avatar-wrapper {
	position: relative;
	margin-right: 20rpx;
}

.user-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	border: 3rpx solid rgba(255, 255, 255, 0.8);
}

.avatar-ring {
	position: absolute;
	top: -3rpx;
	left: -3rpx;
	width: 86rpx;
	height: 86rpx;
	border-radius: 50%;
	border: 2rpx solid transparent;
	background: linear-gradient(45deg, #ff6b91, #4f5fe8) border-box;
	mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
	mask-composite: exclude;
	opacity: 0.6;
}

.user-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.nickname {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 6rpx;
}

.like-time {
	font-size: 22rpx;
	color: #888;
}

/* 平台标识 */
.platform-badge {
	display: flex;
	align-items: center;
	padding: 6rpx 12rpx;
	border-radius: 16rpx;
	border: 1rpx solid;
}

.platform-badge.wx {
	background: rgba(7, 193, 96, 0.1);
	border-color: rgba(7, 193, 96, 0.2);
}

.platform-badge.qq {
	background: rgba(18, 183, 245, 0.1);
	border-color: rgba(18, 183, 245, 0.2);
}

.platform-icon {
	width: 20rpx;
	height: 20rpx;
	margin-right: 6rpx;
	background-size: contain;
	background-repeat: no-repeat;
	background-position: center;
}

.platform-icon.wx {
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2307c160'%3E%3Cpath d='M8.5 12c-.8 0-1.5-.7-1.5-1.5S7.7 9 8.5 9s1.5.7 1.5 1.5-.7 1.5-1.5 1.5zm7 0c-.8 0-1.5-.7-1.5-1.5S14.7 9 15.5 9s1.5.7 1.5 1.5-.7 1.5-1.5 1.5z'/%3E%3C/svg%3E");
}

.platform-icon.qq {
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2312b7f5'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-1.5-1.5L10 14l2-2 2 2 1.5 1.5L14 17l-2-2-2 2z'/%3E%3C/svg%3E");
}

.platform-text {
	font-size: 20rpx;
	font-weight: 500;
}

.platform-badge.wx .platform-text {
	color: #07c160;
}

.platform-badge.qq .platform-text {
	color: #12b7f5;
}

/* 加载更多样式 */
.load-more-wrapper {
	display: flex;
	justify-content: center;
	padding: 30rpx 0;
}

.load-more-indicator {
	display: flex;
	align-items: center;
	padding: 12rpx 24rpx;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 25rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.loading-dots {
	display: flex;
	margin-right: 12rpx;
}

.dot {
	width: 8rpx;
	height: 8rpx;
	border-radius: 50%;
	background: #4f5fe8;
	margin-right: 6rpx;
	animation: dotPulse 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0; margin-right: 0; }

@keyframes dotPulse {
	0%, 80%, 100% {
		transform: scale(0.8);
		opacity: 0.5;
	}
	40% {
		transform: scale(1);
		opacity: 1;
	}
}

.load-more-text {
	font-size: 24rpx;
	color: #666;
}

/* 没有更多样式 */
.no-more-wrapper {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 30rpx 0;
}

.no-more-line {
	flex: 1;
	height: 1rpx;
	background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
}

.no-more-text {
	font-size: 22rpx;
	color: #999;
	margin: 0 20rpx;
}

/* 空状态样式 */
.empty-likes {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 40rpx;
	text-align: center;
}

.empty-illustration {
	position: relative;
	margin-bottom: 40rpx;
}

.empty-heart {
	width: 120rpx;
	height: 120rpx;
	background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ddd' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E") no-repeat center;
	background-size: contain;
	opacity: 0.6;
	animation: emptyHeartFloat 3s ease-in-out infinite;
}

@keyframes emptyHeartFloat {
	0%, 100% { transform: translateY(0); }
	50% { transform: translateY(-10rpx); }
}

.empty-sparkles {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.sparkle {
	position: absolute;
	width: 12rpx;
	height: 12rpx;
	background: radial-gradient(circle, #ff6b91, transparent);
	border-radius: 50%;
	opacity: 0;
	animation: sparkleAnimation 2s ease-in-out infinite;
}

.sparkle-1 {
	top: 20rpx;
	right: 10rpx;
	animation-delay: 0s;
}

.sparkle-2 {
	bottom: 30rpx;
	left: 15rpx;
	animation-delay: 0.7s;
}

.sparkle-3 {
	top: 50rpx;
	left: -10rpx;
	animation-delay: 1.4s;
}

@keyframes sparkleAnimation {
	0%, 100% {
		opacity: 0;
		transform: scale(0.5);
	}
	50% {
		opacity: 1;
		transform: scale(1);
	}
}

.empty-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #666;
	margin-bottom: 12rpx;
}

.empty-subtitle {
	font-size: 26rpx;
	color: #999;
	line-height: 1.5;
	margin-bottom: 40rpx;
}

.empty-action {
	display: flex;
	align-items: center;
	padding: 16rpx 32rpx;
	background: linear-gradient(135deg, rgba(255, 107, 145, 0.1), rgba(79, 95, 232, 0.1));
	border: 1rpx solid rgba(255, 107, 145, 0.2);
	border-radius: 25rpx;
	transition: all 0.3s ease;
}

.empty-action:active {
	transform: scale(0.95);
	background: linear-gradient(135deg, rgba(255, 107, 145, 0.2), rgba(79, 95, 232, 0.2));
}

.share-icon {
	width: 24rpx;
	height: 24rpx;
	background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234f5fe8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8'%3E%3C/path%3E%3Cpolyline points='16 6 12 2 8 6'%3E%3C/polyline%3E%3Cline x1='12' y1='2' x2='12' y2='15'%3E%3C/line%3E%3C/svg%3E") no-repeat center;
	background-size: contain;
	margin-right: 8rpx;
}

.share-text {
	font-size: 26rpx;
	color: #4f5fe8;
	font-weight: 500;
}
</style>
