<template>
	<view class="messages-container">
		<!-- 装饰性背景元素 -->
		<view class="decorative-circle circle-1"></view>
		<view class="decorative-circle circle-2"></view>
		<view class="decorative-shape shape-1"></view>
		<view class="decorative-shape shape-2"></view>
		<view class="decorative-pattern"></view>
		
		<!-- 顶部标题区域 -->
		<view class="messages-header">
			<view class="header-content">
				<text class="page-title">我的留言</text>
				<view class="title-decoration"></view>
			</view>
			
			<view class="back-button" @tap="goBack">
				<view class="back-icon"></view>
				<text class="back-text">返回</text>
			</view>
		</view>
		
		<!-- 加载状态 -->
		<view v-if="isLoading && messages.length === 0" class="loading-state">
			<uni-load-more status="loading" :showText="false"></uni-load-more>
		</view>
		
		<!-- 留言列表 -->
		<scroll-view 
			:scroll-y="needScroll" 
			class="message-list-scroll"
			@scrolltolower="loadMoreMessages"
			:scroll-with-animation="true"
			:show-scrollbar="false"
		>
			<view class="message-list-content" v-if="messages.length > 0">
				<view 
					class="message-card" 
					v-for="(message, index) in messages" 
					:key="message.id" 
					:style="{ animationDelay: `${index * 0.05}s` }"
					@tap="viewMessageDetail(message.name)"
				>
					<view class="message-card-inner">
						<view class="message-top">
							<view class="message-recipient">
								<text class="recipient-label">收信人</text>
								<text class="recipient-name">{{formatName(message.name)}}</text>
							</view>
							<view class="message-status" :class="{'sent': true}"></view>
						</view>
						<text class="message-content">{{message.content}}</text>
						<view class="message-meta">
							<text class="message-date">{{formatTime(message.created_at)}}</text>
							<!-- 新增：点赞数显示 -->
							<view class="likes-info" v-if="message.like_count > 0" @tap.stop="viewLikes(message)">
								<view class="like-icon-small"></view>
								<text class="likes-count">{{message.like_count}}</text>
							</view>
							<button class="share-button" open-type="share" :data-message="message" @tap.stop>
								<text class="share-icon"></text>
								<text class="share-text">分享给Ta</text>
							</button>
						</view>
					</view>
				</view>
				
				<!-- 加载更多提示 -->
				<uni-load-more :status="loadingStatus"></uni-load-more>
			</view>
			
			<!-- 空状态优化 -->
			<view v-else-if="!isLoading" class="empty-messages">
				<view class="empty-illustration"></view>
				<text class="empty-text">还没有留言记录</text>
				<view class="empty-action" @tap="goToWrite">写第一封留言</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				messages: [], // 留言列表
				isLoading: false, // 是否正在加载（首次）
				token: null,
				page: 1, // 当前页码（为以后分页准备）
				pageSize: 15, // 每页数量（为以后分页准备）
				hasMore: true, // 是否还有更多数据
				loadingStatus: 'more', // uni-load-more 的状态: more, loading, noMore
				windowHeight: 0, // 窗口高度
				// 分享相关数据
				shareConfig: {
					title: '想知道，你想对Ta说什么~',
					imageUrl: '/static/share/view1.png',
					path: '/pages/index/index'
				}
			};
		},
		computed: {
			// 判断是否需要滚动功能
			needScroll() {
				return this.messages.length > 3;
			}
		},
		onLoad() {
			this.token = uni.getStorageSync('mailbox_token');
			this.fetchMessages(true); // 首次加载
			this.fetchShareConfig('my_messages'); // 新增：获取页面默认分享配置
			
			// 获取窗口信息
			const info = uni.getSystemInfoSync();
			this.windowHeight = info.windowHeight;
		},
		// 页面分享设置
		onShareAppMessage(res) {
			// 准备默认图片 URL
			let imageUrl = this.shareConfig.imageUrl || '/static/share/index1.png'; // 使用已加载的配置，或默认图
			if (imageUrl.startsWith('/')) {
				imageUrl = this.$baseUrl + imageUrl;
			}

			if (res.from === 'button' && res.target && res.target.dataset && res.target.dataset.message) {
				// 处理来自按钮的分享
				const message = res.target.dataset.message;
				const name = message.name;
				
				// 按钮分享：使用后端配置的通用标题和图片，但路径指向具体留言
				return {
					title: this.shareConfig.title, // 使用从后端获取的 my_messages 页面的通用标题
					path: `/pages/view/view?name=${encodeURIComponent(name)}`, // 路径指向特定收信人
					imageUrl: imageUrl // 使用已处理好的图片 URL
				};
			}
			
			// 默认分享配置 (右上角菜单)
			return {
				title: this.shareConfig.title, // 使用从后端获取的 my_messages 页面的通用标题
				path: this.shareConfig.path, // 使用从后端获取的 my_messages 页面的通用路径 (通常是首页)
				imageUrl: imageUrl // 使用已处理好的图片 URL
			};
		},
		// 分享到朋友圈
		onShareTimeline() {
			return {
				title: '我收到了一条悄悄话，你也来试试吧',
				query: '',
				imageUrl: this.$baseUrl + '/static/share/index1.png'
			};
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},
			
			// 确保有 Token
			async ensureToken() {
				if (this.token) {
					return Promise.resolve();
				}
				return new Promise((resolve, reject) => {
					uni.login({
						success: (loginRes) => {
							if (loginRes.code) {
								this.requestToken(loginRes.code, resolve, reject);
							} else {
								reject(new Error('登录失败'));
							}
						},
						fail: (err) => reject(err)
					});
				});
			},

			requestToken(code, resolve, reject) {
				uni.request({
					url: `${this.$baseUrl}/mailbox/get_openid`,
					method: 'POST',
					data: { code, platform: this.$platform },
					success: (response) => {
						if (response.statusCode === 200 && response.data.token) {
							this.token = response.data.token;
							uni.setStorageSync('mailbox_token', this.token);
							resolve();
						} else {
							reject(new Error('获取认证信息失败'));
						}
					},
					fail: (err) => reject(err)
				});
			},
			
			// 处理 Token 过期
			async handleTokenExpired() {
				const app = getApp();
				this.token = null;
				try {
					await app.ensureLogin();
					this.token = app.globalData.token;
					return true;
				} catch(e){
					console.error('刷新 Token 失败:',e);
					uni.showToast({title:'登录已失效，请稍后重试',icon:'none'});
					return false;
				}
			},

			// 获取消息列表
			async fetchMessages(refresh = false) {
				if (this.isLoading || this.loadingStatus === 'loading') return; // 防止重复加载
				if (refresh) {
					this.page = 1;
					this.messages = [];
					this.hasMore = true;
					this.loadingStatus = 'more';
				} else if (!this.hasMore) {
					this.loadingStatus = 'noMore';
					return; // 没有更多数据了
				}

				this.isLoading = this.messages.length === 0; // 只有首次加载显示全局 loading
				this.loadingStatus = 'loading';

				try {
					await this.ensureToken();
					if (!this.token) throw new Error('Token is missing');

					const res = await uni.request({
						url: `${this.$baseUrl}/mailbox/my-messages`, 
						method: 'GET',
						header: { 'Authorization': `Bearer ${this.token}` }
					});

					if (res.statusCode === 200 && Array.isArray(res.data)) {
						const fetchedMessages = res.data;

						// 为每条留言添加模拟的点赞数（临时用于演示）
						const messagesWithLikes = fetchedMessages.map((message, index) => ({
							...message,
							like_count: Math.floor(Math.random() * 8) // 随机0-7个点赞
						}));

						if (refresh) {
							this.messages = messagesWithLikes;
						} else {
							this.messages = [...this.messages, ...messagesWithLikes];
						}

						// 更新状态
						this.hasMore = false; // 假设一次获取全部，所以没有更多了
						this.loadingStatus = this.hasMore ? 'more' : 'noMore';
						
					} else if (res.statusCode === 401) {
						const success = await this.handleTokenExpired();
						if (success) {
							await this.fetchMessages(refresh);
						}
					} else {
						throw new Error(`API Error: ${res.statusCode}`);
					}
				} catch (error) {
					console.error('获取我的留言失败:', error);
					this.loadingStatus = 'more'; // 出错时允许重试
					uni.showToast({ title: '加载留言失败', icon: 'none' });
				} finally {
					this.isLoading = false;
					// 确保即使出错，loading状态也重置
					if (this.loadingStatus === 'loading') {
						this.loadingStatus = this.hasMore ? 'more' : 'noMore';
					}
				}
			},
			
			// 上拉加载更多（目前后端不支持分页，此方法暂时无效）
			loadMoreMessages() {
				// this.fetchMessages();
			},

			// 查看留言详情 - 修改为跳转到看留言页面
			viewMessageDetail(name) {
				if (!name) {
					uni.showToast({ title: '收信人姓名无效', icon: 'none' });
					return;
				}
				// 跳转到看留言页面，并传递收信人姓名
				uni.navigateTo({
					url: `/pages/view/view?name=${encodeURIComponent(name.trim())}`
				});
			},
			
			// 去写留言
			goToWrite() {
				uni.navigateTo({ url: '/pages/write/write' });
			},

			// 查看点赞详情
			viewLikes(message) {
				if (!message || !message.like_count || message.like_count <= 0) {
					uni.showToast({
						title: '暂无点赞记录',
						icon: 'none'
					});
					return;
				}

				// 跳转到点赞详情页面
				uni.navigateTo({
					url: `/pages/message_likes/message_likes?messageId=${message.id}&messageName=${encodeURIComponent(message.name)}&messageContent=${encodeURIComponent(message.content.substring(0, 50) + (message.content.length > 50 ? '...' : ''))}`
				});
			},
			
			// 格式化方法 - 修改为显示完整名字
			formatName(name) {
				if (!name) return '匿名';
				return name; // 直接返回完整名字
			},
			
			// 返回具体的留言时间
			formatTime(timestamp) {
				if (!timestamp) return '';
				
				try {
					// 尝试直接手动解析日期字符串，避免iOS兼容性问题
					let date;
					
					if (typeof timestamp === 'string') {
						// 处理形如 "2025-04-15 15:42:33" 的格式
						if (timestamp.includes(' ')) {
							// 手动解析日期和时间部分
							const parts = timestamp.split(' ');
							const datePart = parts[0].split('-');
							const timePart = parts[1].split(':');
							
							// 确保年月日都是数字
							const year = parseInt(datePart[0], 10);
							const month = parseInt(datePart[1], 10) - 1; // 月份从0开始
							const day = parseInt(datePart[2], 10);
							
							// 确保时分秒都是数字
							const hours = parseInt(timePart[0], 10);
							const minutes = parseInt(timePart[1], 10);
							const seconds = timePart.length > 2 ? parseInt(timePart[2], 10) : 0;
							
							// 创建日期对象
							date = new Date(year, month, day, hours, minutes, seconds);
						} else {
							// 对于其他格式，先转换为iOS支持的格式
							const formattedTimestamp = timestamp.replace(/-/g, '/');
							date = new Date(formattedTimestamp);
						}
					} else {
						// 如果是时间戳数字
						date = new Date(timestamp);
					}
					
					// 检查date是否有效
					if (isNaN(date.getTime())) {
						console.error('无效的日期格式:', timestamp);
						return '时间未知';
					}
					
					const year = date.getFullYear();
					const month = String(date.getMonth() + 1).padStart(2, '0');
					const day = String(date.getDate()).padStart(2, '0');
					const hours = String(date.getHours()).padStart(2, '0');
					const minutes = String(date.getMinutes()).padStart(2, '0');
					
					return `${year}年${month}月${day}日 ${hours}:${minutes}`;
				} catch (error) {
					console.error('日期解析错误:', error, timestamp);
					return '时间未知';
				}
			},

			// --- 新增：获取分享配置 --- 
			async fetchShareConfig(pageKey) {
				try {
					const res = await uni.request({
						url: `${this.$baseUrl}/mailbox/share-config?pageKey=${pageKey}`,
						method: 'GET',
						header: this.token ? { 'Authorization': `Bearer ${this.token}` } : {}
					});
					
					if (res.statusCode === 200 && res.data && res.data.success) {
						// 更新组件内的分享配置
						this.shareConfig = {
							title: res.data.title || this.shareConfig.title,
							imageUrl: res.data.imageUrl || this.shareConfig.imageUrl,
							path: `/pages/index/index` // 默认分享路径，可在后端配置更佳
						};
						console.log(`页面 [${pageKey}] 分享配置已加载:`, this.shareConfig);
					} else {
						console.error(`获取页面 [${pageKey}] 分享配置失败:`, res.data);
					}
				} catch (error) {
					console.error(`请求页面 [${pageKey}] 分享配置接口失败:`, error);
				}
			},
		}
	}
</script>

<style>
/* 基础样式 */
.messages-container {
	min-height: 100vh;
	height: 100vh; /* 固定高度为视口高度 */
	background: linear-gradient(180deg, #fdfbfb 0%, #f6f7fb 100%);
	padding: 0;
	position: relative;
	overflow: hidden;
	padding-top: var(--status-bar-height);
	box-sizing: border-box; /* 确保padding包含在总高度内 */
	display: flex; /* 使用flex布局 */
	flex-direction: column; /* 垂直排列子元素 */
}

/* 装饰元素 */
.decorative-circle {
	position: absolute;
	border-radius: 50%;
	z-index: 0;
}

.circle-1 {
	width: 400rpx;
	height: 400rpx;
	background: linear-gradient(135deg, rgba(255, 207, 179, 0.7), rgba(255, 158, 158, 0.5));
	top: -200rpx;
	right: -100rpx;
	filter: blur(30rpx);
	animation: floatAnimation 8s ease-in-out infinite;
}

.circle-2 {
	width: 300rpx;
	height: 300rpx;
	background: linear-gradient(135deg, rgba(196, 224, 255, 0.7), rgba(168, 209, 242, 0.5));
	bottom: -150rpx;
	left: -80rpx;
	filter: blur(25rpx);
	animation: floatAnimation 10s ease-in-out infinite reverse;
}

.decorative-shape {
	position: absolute;
	border-radius: 40rpx;
	transform: rotate(30deg);
	z-index: 0;
}

.shape-1 {
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, rgba(255, 184, 222, 0.5), rgba(255, 121, 165, 0.3));
	top: 100rpx;
	left: 80rpx;
	filter: blur(10rpx);
	animation: rotateAnimation 15s linear infinite;
}

.shape-2 {
	width: 160rpx;
	height: 160rpx;
	background: linear-gradient(135deg, rgba(223, 200, 255, 0.6), rgba(192, 160, 236, 0.4));
	bottom: 120rpx;
	right: 100rpx;
	filter: blur(15rpx);
	animation: rotateAnimation 20s linear infinite reverse;
}

.decorative-pattern {
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	background-image: radial-gradient(rgba(255, 255, 255, 0.3) 1px, transparent 1px);
	background-size: 20rpx 20rpx;
	opacity: 0.5;
	z-index: 0;
}

/* 顶部标题区域 */
.messages-header {
	position: relative;
	padding: 40rpx 40rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	z-index: 5;
}

.header-content {
	position: relative;
}

.page-title {
	font-size: 42rpx;
	font-weight: 700;
	color: #333;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

.title-decoration {
	position: absolute;
	height: 8rpx;
	width: 60rpx;
	background: linear-gradient(90deg, #ff6f91, #4f5fe8);
	border-radius: 4rpx;
	bottom: -15rpx;
	left: 5rpx;
}

.back-button {
	display: flex;
	align-items: center;
	padding: 12rpx 24rpx;
	background: rgba(255, 255, 255, 0.8);
	backdrop-filter: blur(10rpx);
	-webkit-backdrop-filter: blur(10rpx);
	border-radius: 30rpx;
	box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	transition: all 0.3s ease;
}

.back-button:active {
	transform: scale(0.96);
	background: rgba(255, 255, 255, 0.9);
}

.back-icon {
	width: 28rpx;
	height: 28rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%234f5fe8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='15 18 9 12 15 6'%3E%3C/polyline%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	background-size: cover;
	margin-right: 8rpx;
}

.back-text {
	font-size: 26rpx;
	color: #4f5fe8;
	font-weight: 500;
}

/* 加载状态 */
.loading-state {
	display: flex;
	justify-content: center;
	align-items: center;
	flex: 1;
	min-height: 300rpx;
	z-index: 5;
	position: relative;
}

/* 留言列表滚动区域 */
.message-list-scroll {
	position: relative;
	flex: 1; /* 自动填充剩余空间 */
	height: 0; /* 配合flex:1工作 */
	z-index: 5;
	overflow: hidden; /* 防止出现双重滚动条 */
}

.message-list-content {
	padding: 20rpx 30rpx 120rpx;
	height: 100%; /* 确保内容填满scroll-view */
}

/* 留言卡片样式 */
.message-card {
	margin-bottom: 30rpx;
	position: relative;
	perspective: 1000rpx;
	animation: cardFadeIn 0.5s ease forwards;
	opacity: 0;
	transform: translateY(20rpx);
}

@keyframes cardFadeIn {
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.message-card-inner {
	padding: 30rpx;
	background: rgba(255, 255, 255, 0.8);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-radius: 24rpx;
	box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.05);
	border: 1rpx solid rgba(255, 255, 255, 0.6);
	display: flex;
	flex-direction: column;
	transition: all 0.3s ease;
	transform-style: preserve-3d;
	position: relative;
	overflow: hidden;
}

.message-card-inner::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 6rpx;
	background: linear-gradient(90deg, #ff6f91, #4f5fe8);
	opacity: 0.8;
}

.message-card:active .message-card-inner {
	transform: rotateY(5deg) scale(0.98);
	box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.03);
}

.message-top {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.message-recipient {
	display: flex;
	align-items: center;
}

.recipient-label {
	font-size: 22rpx;
	color: #999;
	margin-right: 10rpx;
}

.recipient-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #4f5fe8;
	position: relative;
}

.recipient-name::after {
	content: '';
	position: absolute;
	left: 0;
	bottom: -4rpx;
	width: 100%;
	height: 2rpx;
	background: currentColor;
	opacity: 0.3;
	transform: scaleX(0.7);
	transform-origin: left;
	transition: transform 0.3s ease;
}

.message-card:active .recipient-name::after {
	transform: scaleX(1);
}

.message-content {
	font-size: 28rpx;
	color: #444;
	line-height: 1.7;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-bottom: 20rpx;
	padding: 10rpx;
	background-color: rgba(255, 255, 255, 0.5);
	border-radius: 12rpx;
	border-left: 3rpx solid #f0f0f0;
	white-space: pre-wrap;
}

.message-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.message-date {
	font-size: 22rpx;
	color: #888;
	background: rgba(0, 0, 0, 0.03);
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
}

/* 点赞信息样式 */
.likes-info {
	display: flex;
	align-items: center;
	background: linear-gradient(135deg, rgba(255, 107, 145, 0.1), rgba(79, 95, 232, 0.1));
	padding: 6rpx 12rpx;
	border-radius: 16rpx;
	border: 1rpx solid rgba(255, 107, 145, 0.2);
	transition: all 0.3s ease;
	cursor: pointer;
}

.likes-info:active {
	background: linear-gradient(135deg, rgba(255, 107, 145, 0.2), rgba(79, 95, 232, 0.2));
	transform: scale(0.95);
}

.like-icon-small {
	width: 20rpx;
	height: 20rpx;
	background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ff6b91' stroke='%23ff6b91' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E") no-repeat center;
	background-size: contain;
	margin-right: 6rpx;
	animation: likeIconPulse 2s ease-in-out infinite;
}

@keyframes likeIconPulse {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.1); }
}

.likes-count {
	font-size: 20rpx;
	color: #ff6b91;
	font-weight: 600;
}

/* 分享按钮样式 */
.share-button {
	display: flex;
	align-items: center;
	background: rgba(79, 95, 232, 0.1);
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
	transition: all 0.3s ease;
	/* 为 button 元素添加的新样式 */
	font-size: inherit;
	border: none;
	line-height: normal;
	box-sizing: border-box;
	margin: 0;
	min-height: auto;
	color: inherit;
	text-align: left;
}

/* 移除 button 默认样式 (修改写法以兼容 WXSS) */
.share-button::after {
	display: none;
}

.share-button:active {
	background: rgba(79, 95, 232, 0.2);
	transform: scale(0.95);
}

.share-icon {
	width: 24rpx;
	height: 24rpx;
	margin-right: 6rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234f5fe8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8'%3E%3C/path%3E%3Cpolyline points='16 6 12 2 8 6'%3E%3C/polyline%3E%3Cline x1='12' y1='2' x2='12' y2='15'%3E%3C/line%3E%3C/svg%3E");
	background-size: contain;
	background-repeat: no-repeat;
	background-position: center;
}

.share-text {
	font-size: 22rpx;
	color: #4f5fe8;
	font-weight: 500;
}

.message-status {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
}

.message-status.sent {
	background: #4f5fe8;
	box-shadow: 0 0 10rpx rgba(79, 95, 232, 0.5);
	position: relative;
}

.message-status.sent::after {
	content: '';
	position: absolute;
	top: -4rpx;
	left: -4rpx;
	right: -4rpx;
	bottom: -4rpx;
	border-radius: 50%;
	border: 1rpx solid currentColor;
	animation: statusPulse 2s ease-in-out infinite;
}

/* 空状态优化 */
.empty-messages {
	position: relative;
	margin: 40rpx 30rpx;
	padding: 60rpx 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.7);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-radius: 30rpx;
	box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.05);
	border: 1rpx solid rgba(255, 255, 255, 0.6);
	z-index: 5;
	min-height: 500rpx; /* 确保空状态有足够高度 */
	flex: 1; /* 填充剩余空间 */
	margin-bottom: 40rpx; /* 底部留出空间 */
}

.empty-illustration {
	width: 160rpx;
	height: 160rpx;
	margin-bottom: 30rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23cccccc' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z'%3E%3C/path%3E%3Cpolyline points='22,6 12,13 2,6'%3E%3C/polyline%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	opacity: 0.5;
}

.empty-text {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 30rpx;
}

.empty-action {
	font-size: 26rpx;
	color: #4f5fe8;
	padding: 12rpx 40rpx;
	background: rgba(79, 95, 232, 0.1);
	border-radius: 30rpx;
	transition: all 0.3s ease;
}

.empty-action:active {
	transform: scale(0.95);
	background: rgba(79, 95, 232, 0.15);
}

/* 动画关键帧 */
@keyframes floatAnimation {
	0%, 100% {
		transform: translate(0, 0);
	}
	50% {
		transform: translate(15rpx, 15rpx);
	}
}

@keyframes rotateAnimation {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

@keyframes statusPulse {
	0%, 100% {
		transform: scale(1);
		opacity: 0.5;
	}
	50% {
		transform: scale(1.5);
		opacity: 0.1;
	}
}
</style>
